# MaterialSearch 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置值

# 运行环境 (development/testing/staging/production)
ENVIRONMENT=development

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=materialsearch
DB_USER=materialsearch
DB_PASSWORD=your_password_here

# 生产环境数据库配置
PROD_DB_HOST=localhost
PROD_DB_USER=materialsearch
PROD_DB_PASSWORD=your_production_password_here

# 预发布环境数据库配置
STAGING_DB_PASSWORD=your_staging_password_here

# 服务器配置
SERVER_HOST=127.0.0.1
SERVER_PORT=8000
SERVER_DEBUG=true

# 模型配置
MODEL_NAME=OFA-Sys/chinese-clip-vit-base-patch16
MODEL_BACKEND=transformers
MODEL_DEVICE=auto
MODEL_CACHE_DIR=./models

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=./logs/app.log

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production

# Hugging Face配置
HF_ENDPOINT=https://hf-mirror.com
HF_TOKEN=your_huggingface_token_here

# 文件存储配置
UPLOAD_DIR=./uploads
STATIC_DIR=./static
MODELS_DIR=./models

# 任务配置
MAX_CONCURRENT_UPLOADS=10
UPLOAD_TIMEOUT=300
BACKGROUND_WORKERS=4

# 搜索配置
SEARCH_CACHE_SIZE=64
POSITIVE_THRESHOLD=36
NEGATIVE_THRESHOLD=36
IMAGE_THRESHOLD=85
