# MaterialSearch

基于AI的本地媒体搜索系统，支持通过自然语言描述、图片相似度等方式快速检索本地图片和视频文件。

## 功能特性

- **多模态搜索**：支持文本搜索、图片搜索、组合搜索
- **实时处理**：支持实时添加和处理媒体文件
- **扩展属性**：支持自定义业务元数据
- **高性能**：基于pgvector的向量搜索，支持GPU加速
- **易部署**：支持Docker部署，配置简单

## 技术栈

- **后端**：FastAPI + Tortoise ORM + PostgreSQL + pgvector
- **AI模型**：CLIP (支持中英文)
- **前端**：Vue.js 3 + 原生HTML/CSS
- **部署**：Docker + Docker Compose

## 快速开始

### 环境要求

- Python 3.9+
- PostgreSQL 14+ (带pgvector扩展)
- 4GB+ RAM
- (可选) NVIDIA GPU

### 1. 克隆项目

```bash
git clone <repository-url>
cd MaterialSearch
```

### 2. 安装依赖

使用uv包管理器：

```bash
# 安装uv
pip install uv

# 安装项目依赖
uv pip install -e .
```

### 3. 配置环境

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

### 4. 初始化数据库

```bash
# 启动数据库 (使用Docker)
docker-compose -f docker-compose.dev.yml up -d postgres

# 初始化数据库表结构
python -m app.init_db
```

### 5. 启动服务

```bash
# 开发模式启动
python -m app.main

# 或使用CLI工具
python -m app.cli serve --reload
```

### 6. 访问应用

打开浏览器访问：http://localhost:8000

## Docker部署

### 开发环境

```bash
# 启动开发环境数据库
docker-compose -f docker-compose.dev.yml up -d

# 本地运行应用
python -m app.main
```

### 生产环境

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f materialsearch
```

## 配置说明

### 环境变量

主要环境变量说明：

- `ENVIRONMENT`: 运行环境 (development/production)
- `DB_HOST`: 数据库主机地址
- `DB_PASSWORD`: 数据库密码
- `MODEL_NAME`: AI模型名称
- `MODEL_DEVICE`: 计算设备 (auto/cpu/cuda)

### 配置文件

配置文件位于 `config/` 目录：

- `base.yaml`: 基础配置
- `development.yaml`: 开发环境配置
- `production.yaml`: 生产环境配置

## API文档

启动服务后访问：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 主要API端点

- `POST /api/search/text`: 文本搜索
- `POST /api/search/image`: 图片搜索
- `POST /api/content/upload`: 文件上传
- `GET /api/system/status`: 系统状态

## 命令行工具

```bash
# 查看帮助
python -m app.cli --help

# 初始化数据库
python -m app.cli init

# 扫描文件
python -m app.cli scan /path/to/images

# 查看配置
python -m app.cli config

# 查看系统状态
python -m app.cli status

# 启动服务
python -m app.cli serve --host 0.0.0.0 --port 8000
```

## 开发指南

### 项目结构

```
MaterialSearch/
├── app/                    # 应用代码
│   ├── api/               # API路由
│   ├── core/              # 核心业务逻辑
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   ├── utils/             # 工具模块
│   └── config/            # 配置管理
├── config/                # 配置文件
├── static/                # 静态文件
├── templates/             # 模板文件
├── logs/                  # 日志文件
├── models/                # AI模型缓存
└── test_data/             # 测试数据
```

### 开发环境设置

```bash
# 安装开发依赖
uv pip install -e ".[dev]"

# 安装pre-commit钩子
pre-commit install

# 运行测试
pytest

# 代码格式化
black app/
isort app/

# 类型检查
mypy app/
```

## 性能优化

### GPU加速

```bash
# 安装GPU版本依赖
uv pip install -e ".[gpu]"

# 配置使用GPU
export MODEL_DEVICE=cuda
```

### 批量处理优化

在配置文件中调整：

```yaml
model:
  batch_size: 16  # 根据GPU显存调整
  
scan:
  auto_save_interval: 50  # 减少保存频率
```

## 故障排除

### 常见问题

1. **模型下载慢**
   ```bash
   export HF_ENDPOINT=https://hf-mirror.com
   ```

2. **GPU内存不足**
   ```yaml
   model:
     batch_size: 2
     precision: "fp16"
   ```

3. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 确认pgvector扩展已安装
   - 验证连接参数

### 日志查看

```bash
# 实时日志
tail -f logs/app.log

# 错误日志
grep "ERROR" logs/app.log

# Docker日志
docker-compose logs -f materialsearch
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

- 文档：[Docs/README.md](Docs/README.md)
- 问题反馈：GitHub Issues
- 讨论：GitHub Discussions
