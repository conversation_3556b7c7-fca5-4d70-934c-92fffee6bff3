# MaterialSearch 基础配置文件
# 所有环境共享的通用配置

# 应用基础配置
app:
  name: "MaterialSearch"
  version: "2.0.0"
  description: "AI-powered local media search system"

# 服务器配置
server:
  host: "127.0.0.1"
  port: 8085
  debug: false
  workers: 1
  reload: false

# 数据库配置
database:
  host: "localhost"
  port: 5432
  name: "materialsearch"
  user: "materialsearch"
  password: ""
  pool_size: 20
  vector_dimension: 512
  timeout: 30

# 模型配置
model:
  name: "OFA-Sys/chinese-clip-vit-base-patch16"
  backend: "transformers"  # transformers/onnx/openvino
  precision: "fp32"        # fp32/fp16/int8
  device: "auto"           # auto/cpu/cuda/mps
  cache_dir: "./models"
  batch_size: 4

# 扫描配置
scan:
  assets_paths:
    - "./test_data/images"
    - "./test_data/videos"
  skip_paths:
    - "/tmp"
    - "/var/cache"
  image_extensions:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".gif"
    - ".heic"
    - ".webp"
    - ".bmp"
  video_extensions:
    - ".mp4"
    - ".flv"
    - ".mov"
    - ".mkv"
    - ".webm"
    - ".avi"
  frame_interval: 2
  image_min_width: 64
  image_min_height: 64
  auto_save_interval: 100

# 搜索配置
search:
  cache_size: 64
  positive_threshold: 36
  negative_threshold: 36
  image_threshold: 85

# 任务处理配置
task:
  sync_process_timeout: 30
  task_lock_timeout: 30
  max_retry_attempts: 3
  cleanup_interval: 24
  background_workers: 4
  realtime_enabled: true
  max_concurrent_uploads: 10
  upload_timeout: 300
  auto_async_threshold: 10485760  # 10MB

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file_path: "./logs/app.log"
  max_size: 100
  backup_count: 5
  rotation: "size"
  console_enabled: true
  file_enabled: true
