# MaterialSearch 生产环境配置
# 生产环境特定的配置覆盖

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  debug: false
  reload: false

# 数据库配置
database:
  host: "${PROD_DB_HOST:localhost}"
  name: "materialsearch"
  user: "${PROD_DB_USER:materialsearch}"
  password: "${PROD_DB_PASSWORD}"
  pool_size: 50

# 模型配置
model:
  backend: "onnx"
  precision: "fp16"
  batch_size: 16
  cache_dir: "/opt/models"

# 扫描配置
scan:
  assets_paths:
    - "/data/images"
    - "/data/videos"
  skip_paths:
    - "/tmp"
    - "/var/cache"
    - "/data/backup"

# 搜索配置
search:
  cache_size: 256  # 生产环境更大缓存

# 日志配置
logging:
  level: "ERROR"
  file_path: "/var/log/materialsearch/production.log"
  format: "json"
  console_enabled: false
  max_size: 500
  backup_count: 10

# 任务配置
task:
  background_workers: 8
  max_concurrent_uploads: 50
  cleanup_interval: 168  # 一周清理一次
