# MaterialSearch Dockerfile
# 基于Python 3.11的官方镜像

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV ENVIRONMENT=production

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install uv

# 复制项目文件
COPY pyproject.toml ./
COPY app/ ./app/
COPY config/ ./config/
COPY static/ ./static/
COPY templates/ ./templates/

# 创建必要的目录
RUN mkdir -p logs uploads models test_data

# 安装Python依赖
RUN uv pip install --system -e .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash materialsearch
RUN chown -R materialsearch:materialsearch /app
USER materialsearch

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/system/health || exit 1

# 启动命令
CMD ["python", "-m", "app.main"]
