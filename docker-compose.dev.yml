# MaterialSearch 开发环境 Docker Compose 配置
version: '3.8'

services:
  # PostgreSQL 数据库 (仅开发环境)
  postgres:
    image: pgvector/pgvector:pg14
    container_name: materialsearch-dev-db
    environment:
      - POSTGRES_DB=materialsearch_dev
      - POSTGRES_USER=materialsearch
      - POSTGRES_PASSWORD=dev_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U materialsearch -d materialsearch_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - materialsearch-dev-network

  # Redis (开发环境)
  redis:
    image: redis:7-alpine
    container_name: materialsearch-dev-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - materialsearch-dev-network

# 数据卷
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

# 网络
networks:
  materialsearch-dev-network:
    driver: bridge
