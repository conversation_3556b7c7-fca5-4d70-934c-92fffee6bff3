[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "materialsearch"
version = "2.0.0"
description = "AI-powered local media search system"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "MaterialSearch Team", email = "<EMAIL>"},
]
keywords = ["ai", "search", "image", "video", "clip", "fastapi"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Multimedia :: Graphics",
    "Topic :: Multimedia :: Video",
]

dependencies = [
    # Web框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    
    # 数据库和ORM
    "tortoise-orm[asyncpg]>=0.20.0",
    "asyncpg>=0.29.0",
    "pgvector>=0.2.4",
    
    # AI和机器学习
    "transformers>=4.28.1",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "accelerate>=0.25.0",
    "sentence-transformers>=2.2.2",
    
    # ONNX支持
    "onnxruntime>=1.16.0",
    "onnxruntime-gpu>=1.16.0; sys_platform != 'darwin'",
    
    # OpenVINO支持
    "openvino>=2023.1.0",
    
    # 图像和视频处理
    "opencv-python>=4.7.0",
    "pillow>=8.1.0",
    "pillow-heif>=0.14.0",
    
    # 数据处理
    "numpy>=1.20.3",
    "pandas>=1.3.0",
    
    # 配置和环境
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    
    # HTTP客户端
    "httpx>=0.25.0",
    "aiofiles>=23.0.0",
    
    # 工具库
    "click>=8.0.0",
    "rich>=13.0.0",
    "tqdm>=4.64.0",
    
    # 日期时间
    "python-dateutil>=2.8.0",
    
    # 加密和安全
    "cryptography>=41.0.0",
    "passlib[bcrypt]>=1.7.4",
    
    # 监控和日志
    "structlog>=23.0.0",
    "loguru>=0.7.0",
]

[project.optional-dependencies]
dev = [
    # 测试
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    
    # 代码质量
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    
    # 开发工具
    "pre-commit>=3.4.0",
    "jupyter>=1.0.0",
    "ipython>=8.0.0",
]

production = [
    # 生产环境优化
    "gunicorn>=21.0.0",
    "gevent>=23.0.0",
    
    # 监控
    "prometheus-client>=0.17.0",
    "psutil>=5.9.0",
]

gpu = [
    # GPU加速
    "torch[cuda]>=2.0.0",
    "torchvision[cuda]>=0.15.0",
    "onnxruntime-gpu>=1.16.0",
]

[project.urls]
Homepage = "https://github.com/materialsearch/materialsearch"
Documentation = "https://docs.materialsearch.com"
Repository = "https://github.com/materialsearch/materialsearch"
Issues = "https://github.com/materialsearch/materialsearch/issues"

[project.scripts]
materialsearch = "app.main:main"
ms-init = "app.cli:init_command"
ms-scan = "app.cli:scan_command"
ms-config = "app.cli:config_command"

[tool.hatch.version]
path = "app/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "PIL.*",
    "torch.*",
    "torchvision.*",
    "transformers.*",
    "sentence_transformers.*",
    "onnxruntime.*",
    "openvino.*",
    "pgvector.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
