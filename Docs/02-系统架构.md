# MaterialSearch 系统架构

## 整体架构

**系统架构**：Web前端 → FastAPI后端 → PostgreSQL+pgvector → CLIP模型 → 本地文件系统

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue.js 前端    │───▶│  FastAPI 后端   │───▶│ PostgreSQL DB   │
│   - 搜索界面     │    │   - API接口     │    │  + pgvector     │
│   - 文件管理     │    │   - 业务逻辑    │    │   - 向量存储    │
│   - 进度监控     │    │   - 任务管理    │    │   - 元数据      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   CLIP 模型     │───▶│   文件系统      │
                       │   - 特征提取    │    │   - 图片文件    │
                       │   - 多后端支持  │    │   - 视频文件    │
                       │   - 热更新      │    │   - 索引缓存    │
                       └─────────────────┘    └─────────────────┘
```

## 核心模块

### 1. 内容管理模块 (content_manager.py)
- **ContentManager类**：负责实时内容管理和处理
- **功能**：
  - 实时添加单个或多个媒体文件
  - 即时特征提取和索引更新
  - 文件系统监控（可选）
  - 内容删除和更新
  - 处理状态跟踪

### 2. 扫描模块 (scan.py)
- **Scanner类**：负责批量文件扫描和处理
- **功能**：
  - 遍历指定目录
  - 检测文件变更（基于修改时间或校验和）
  - 批量处理图片和视频
  - 支持断点续传

### 3. 模型管理模块 (model_manager.py)
- **ModelManager类**：统一的模型加载和管理
- **功能**：
  - 多后端模型加载（Transformers/ONNX/OpenVINO）
  - 动态模型切换和热更新
  - 模型缓存和内存管理
  - 推理性能监控和优化

### 4. 特征处理模块 (process_assets.py)
- **图片处理**：
  - 支持多种格式（JPG、PNG、GIF、HEIC、WebP、BMP）
  - 批量特征提取
  - 尺寸过滤
- **视频处理**：
  - 按帧间隔提取关键帧
  - 支持多种格式（MP4、FLV、MOV、MKV、WebM、AVI）
  - 生成器模式处理大文件
- **文本处理**：
  - 自然语言特征提取
  - 支持中英文

### 5. 搜索模块 (search.py)
- **特征匹配**：余弦相似度计算
- **阈值过滤**：正向/反向提示词过滤
- **结果排序**：按相似度分数排序
- **缓存机制**：LRU缓存提高查询效率

### 6. 数据库模块 (models.py, database.py)
- **ORM映射**：Tortoise ORM模型定义
- **异步操作**：异步数据库增删改查
- **向量操作**：pgvector向量相似度查询
- **连接管理**：异步数据库连接池

### 7. 日志管理模块 (utils/logger.py)
- **Logger类**：统一的日志管理器
- **功能**：
  - 全局日志配置和初始化
  - 分级日志输出（DEBUG/INFO/WARNING/ERROR/CRITICAL）
  - 日志文件轮转和归档
  - 结构化日志格式（JSON/文本）
  - 性能日志和业务日志分离

### 8. Web服务模块 (routes.py)
- **API接口**：RESTful API设计
- **文件服务**：图片/视频文件访问
- **上传处理**：文件上传和临时存储

## 推荐的FastAPI项目结构

```
fastapi_materialsearch/
├── app/
│   ├── __init__.py
│   ├── main.py                  # FastAPI应用入口
│   ├── config.py                # 配置管理
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── database.py          # Tortoise ORM模型
│   │   └── schemas.py           # Pydantic模型
│   ├── api/                     # API路由
│   │   ├── __init__.py
│   │   ├── deps.py              # 依赖注入
│   │   ├── search.py            # 搜索接口
│   │   ├── content.py           # 内容管理
│   │   ├── tasks.py             # 任务管理
│   │   └── models.py            # 模型管理
│   ├── core/                    # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── scanner.py           # 文件扫描和处理
│   │   ├── search.py            # 搜索引擎
│   │   └── models.py            # 模型管理
│   ├── utils/                   # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py            # 统一日志管理
│   │   ├── response.py          # 统一响应格式
│   │   ├── exceptions.py        # 自定义异常和错误码
│   │   └── helpers.py           # 辅助函数
│   └── services/                # 业务服务层
│       ├── __init__.py
│       ├── content_service.py   # 内容管理服务
│       ├── search_service.py    # 搜索服务
│       └── model_service.py     # 模型服务
├── static/                      # 静态文件
├── logs/                        # 日志文件目录
├── models/                      # 模型文件缓存
├── uploads/                     # 上传文件临时目录
├── requirements.txt
├── .env
└── docker-compose.yml
```

## 数据流架构

### 1. 文件处理流程
```
文件输入 → 格式验证 → 特征提取 → 向量存储 → 索引更新 → 搜索可用
```

### 2. 搜索处理流程
```
搜索请求 → 特征提取 → 向量匹配 → 结果过滤 → 排序返回
```

### 3. 实时添加流程
```
API请求 → 文件接收 → 异步处理 → 状态跟踪 → 结果返回
```

## 关键设计原则

### 1. 模块化设计
- 每个模块职责单一，便于维护和扩展
- 模块间通过标准接口通信
- 支持模块独立部署和升级

### 2. 异步处理
- 使用FastAPI的异步特性提高并发性能
- 长时间任务使用后台处理避免阻塞
- 支持任务状态跟踪和进度反馈

### 3. 扩展性设计
- 支持多种CLIP模型和推理后端
- 灵活的扩展属性系统
- 可配置的业务逻辑和处理流程

### 4. 性能优化
- 向量索引加速搜索
- LRU缓存减少重复计算
- 批量处理提高吞吐量
- 连接池复用数据库连接

### 5. 容错设计
- 完善的错误处理和重试机制
- 任务状态持久化和恢复
- 多进程部署防重复处理
- 优雅的服务降级策略 