# MaterialSearch 项目概述

## 项目简介

MaterialSearch 是一个基于AI的本地图片和视频搜索系统，支持通过自然语言描述、图片相似度等方式快速检索本地媒体文件。该项目使用CLIP模型实现多模态搜索功能。

## 核心功能

### 1. 搜索功能
- **文字搜图**：通过自然语言描述搜索相关图片，返回图片及扩展属性
- **以图搜图**：上传图片或选择已有图片进行相似图片搜索
- **文字搜视频**：通过描述搜索视频片段，返回匹配的时间段及相关信息
- **以图搜视频**：通过图片搜索包含相似内容的视频片段
- **属性搜索**：支持按扩展属性进行过滤搜索（订单ID、标签等）
- **组合搜索**：支持视觉相似度与属性条件的组合查询
- **图文相似度计算**：计算图片与文本描述的匹配分数

### 2. 实时内容管理功能
- **实时添加**：支持通过API实时添加单个图片或视频到搜索索引
- **扩展属性**：支持添加业务相关的元数据（订单ID、名称、备注、标签等）
- **即时处理**：新添加的媒体文件立即进行特征提取和索引
- **动态更新**：无需重启服务即可搜索到新添加的内容
- **批量导入**：支持一次性导入多个文件及其扩展属性
- **属性更新**：支持更新已索引内容的扩展属性
- **文件监控**：可选的文件系统监控，自动检测新文件
- **内容删除**：支持从索引中删除指定的图片或视频

### 3. 媒体处理功能
- **自动扫描**：定时扫描指定目录下的图片和视频文件
- **特征提取**：使用CLIP模型提取图片和视频帧的特征向量
- **增量更新**：支持文件变更检测，只处理新增或修改的文件
- **批量处理**：支持批量处理图片以提高效率
- **实时处理**：单文件快速处理模式，优化用户体验

### 4. 管理功能
- **Web界面**：提供友好的Web操作界面
- **进度监控**：实时显示扫描进度和系统状态
- **视频片段下载**：支持下载匹配的视频片段
- **内容管理**：查看、编辑、删除已索引的媒体文件
- **实时状态**：显示实时添加的文件处理状态
- **索引统计**：实时显示索引中的文件数量和存储信息

## 技术选型

### 后端技术栈
- **Web框架**：FastAPI 0.104+（推荐）或 Flask 2.2.2+
- **数据库**：PostgreSQL 14+ + pgvector扩展
- **ORM框架**：Tortoise ORM 0.20+（异步ORM）
- **AI模型**：
  - Transformers 4.28.1+（Hugging Face）
  - ONNX Runtime 1.16+（ONNX推理）
  - OpenVINO 2023.1+（Intel优化推理）
  - 支持多种CLIP模型和推理后端
- **图像处理**：
  - OpenCV 4.7.0+（视频处理）
  - Pillow 8.1.0+（图片处理）
  - pillow-heif 0.14.0+（HEIC格式支持）
- **向量计算**：
  - NumPy 1.20.3+
  - PostgreSQL pgvector扩展（向量相似度计算和存储）
- **深度学习**：
  - PyTorch 2.0+
  - Accelerate 1.5.0+

### 前端技术栈
- **框架**：Vue.js 3
- **HTTP客户端**：Axios
- **国际化**：Vue I18n
- **UI组件**：原生HTML/CSS + JavaScript
- **工具库**：Clipboard.js

### 数据存储
- **主数据库**：PostgreSQL 14+（存储图片和视频元数据及特征向量）
- **向量扩展**：pgvector扩展（高效的向量存储和相似度计算）
- **ORM框架**：Tortoise ORM（异步数据库操作）
- **表结构**：
  - `images`表：图片路径、修改时间、特征向量、校验和、处理状态
  - `videos`表：视频路径、帧时间、修改时间、特征向量、校验和、处理状态
  - `tasks`表：处理任务记录、状态跟踪
- **文件存储**：本地文件系统或云存储

## 支持的文件格式

### 图片格式
- JPG、JPEG、PNG、GIF、HEIC、WebP、BMP

### 视频格式
- MP4、FLV、MOV、MKV、WebM、AVI

## 多语言支持
- 中文CLIP模型：OFA-Sys/chinese-clip-vit-*
- 英文CLIP模型：openai/clip-vit-*
- 前端国际化：中英文界面

## 硬件要求
- **CPU**：推荐 x86_64 或 ARM64 架构
- **内存**：最低2GB，推荐4GB以上
- **存储**：根据媒体文件数量确定
- **GPU**：可选，支持CUDA/MPS/DirectML加速

## 项目特点

1. **多模态搜索**：支持文本和图像的跨模态搜索
2. **实时处理**：支持实时添加和处理媒体文件
3. **扩展性强**：支持自定义扩展属性和业务数据关联
4. **性能优化**：批量处理、缓存策略、向量索引等多重优化
5. **部署灵活**：支持源码部署、Docker部署等多种方式
6. **易于扩展**：模块化设计，支持功能扩展和定制 