# MaterialSearch 数据库设计

## 数据库架构

### PostgreSQL + pgvector 架构优势
- **向量存储**：原生支持高维向量存储和索引
- **相似度计算**：内置余弦相似度、欧几里得距离等计算
- **ACID特性**：保证数据一致性和事务安全
- **扩展性**：支持水平扩展和读写分离
- **性能优化**：向量索引（IVFFlat、HNSW）加速查询

### ORM框架：Tortoise ORM
- **异步支持**：完全异步的数据库操作
- **类型安全**：完整的类型提示支持
- **迁移管理**：自动生成和管理数据库迁移
- **多数据库**：支持PostgreSQL、MySQL、SQLite等
- **查询构建器**：直观的查询API

## 枚举类型定义

### ContentSourceType（内容来源类型）
- **SCAN**: 目录扫描获得
- **UPLOAD**: 用户上传
- **API**: API接口添加

### ProcessStatus（处理状态）
- **PENDING**: 等待处理
- **PROCESSING**: 正在处理
- **COMPLETED**: 处理完成
- **FAILED**: 处理失败

### TaskType（任务类型）
- **SINGLE_FILE**: 单文件处理
- **BATCH_FILES**: 批量文件处理
- **DIRECTORY_SCAN**: 目录扫描

### TaskStatus（任务状态）
- **PENDING**: 等待开始
- **RUNNING**: 正在执行
- **COMPLETED**: 执行完成
- **FAILED**: 执行失败
- **CANCELLED**: 已取消
- **LOCKED**: 已锁定（防止多进程重复处理）

### SearchType（搜索类型）
- **VISUAL_ONLY**: 纯视觉搜索
- **ATTRIBUTE_ONLY**: 纯属性搜索
- **COMBINED**: 组合搜索
- **WEIGHTED**: 加权搜索

### 业务分类
使用简单的字符串字段存储业务类型，不预设具体枚举值，保持系统的通用性和灵活性。用户可以根据实际需求自定义业务类型标识。

## 数据表设计

### Images表模型
```sql
CREATE TABLE images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    path TEXT UNIQUE NOT NULL,                    -- 文件路径
    filename TEXT NOT NULL,                       -- 文件名
    file_size BIGINT,                            -- 文件大小
    checksum TEXT,                               -- 文件校验和（MD5/SHA1）
    features VECTOR(512),                        -- 特征向量（pgvector类型）
    source_type content_source_type_enum,        -- 来源类型枚举
    business_type business_type_enum,            -- 业务类型枚举
    process_status process_status_enum,          -- 处理状态枚举
    created_at TIMESTAMPTZ DEFAULT NOW(),       -- 创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW(),       -- 更新时间
    processed_at TIMESTAMPTZ,                   -- 处理完成时间
    error_message TEXT,                         -- 错误信息
    metadata JSONB                              -- 扩展属性（用户自定义JSON结构）
);

-- 索引
CREATE INDEX idx_images_path ON images(path);
CREATE INDEX idx_images_source_type ON images(source_type);
CREATE INDEX idx_images_process_status ON images(process_status);
CREATE INDEX idx_images_business_type ON images(business_type);
CREATE INDEX idx_images_metadata ON images USING GIN(metadata);
CREATE INDEX idx_images_features ON images USING ivfflat(features vector_cosine_ops);
```

### Videos表模型
```sql
CREATE TABLE videos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    path TEXT NOT NULL,                          -- 视频文件路径
    frame_time FLOAT NOT NULL,                   -- 帧时间戳
    frame_index INTEGER NOT NULL,               -- 帧序号
    features VECTOR(512),                        -- 帧特征向量
    duration FLOAT,                              -- 视频总时长
    fps FLOAT,                                   -- 帧率
    resolution TEXT,                             -- 分辨率
    source_type content_source_type_enum,        -- 来源类型枚举
    process_status process_status_enum,          -- 处理状态枚举
    created_at TIMESTAMPTZ DEFAULT NOW(),       -- 创建时间
    updated_at TIMESTAMPTZ DEFAULT NOW(),       -- 更新时间
    metadata JSONB                               -- 扩展属性
);

-- 索引
CREATE INDEX idx_videos_path ON videos(path);
CREATE INDEX idx_videos_frame_time ON videos(frame_time);
CREATE INDEX idx_videos_process_status ON videos(process_status);
CREATE INDEX idx_videos_metadata ON videos USING GIN(metadata);
CREATE INDEX idx_videos_features ON videos USING ivfflat(features vector_cosine_ops);
```

### Tasks表模型
```sql
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_type task_type_enum NOT NULL,           -- 任务类型枚举
    status task_status_enum DEFAULT 'PENDING',   -- 任务状态枚举
    total_files INTEGER DEFAULT 0,              -- 总文件数
    processed_files INTEGER DEFAULT 0,          -- 已处理文件数
    failed_files INTEGER DEFAULT 0,             -- 失败文件数
    progress FLOAT DEFAULT 0,                   -- 进度百分比
    started_at TIMESTAMPTZ,                     -- 开始时间
    completed_at TIMESTAMPTZ,                   -- 完成时间
    locked_at TIMESTAMPTZ,                      -- 锁定时间
    locked_by TEXT,                             -- 锁定进程ID
    retry_count INTEGER DEFAULT 0,             -- 重试次数
    max_retries INTEGER DEFAULT 3,             -- 最大重试次数
    created_by TEXT,                            -- 创建用户
    metadata JSONB                              -- 任务元数据
);

-- 索引
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_type ON tasks(task_type);
CREATE INDEX idx_tasks_locked_at ON tasks(locked_at);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
```

## Tortoise ORM 模型定义

### Images模型
```python
from tortoise.models import Model
from tortoise import fields
from pgvector.tortoise import VectorField

class Images(Model):
    id = fields.UUIDField(pk=True)
    path = fields.CharField(max_length=1000, unique=True)
    filename = fields.CharField(max_length=255)
    file_size = fields.BigIntField(null=True)
    checksum = fields.CharField(max_length=64, null=True)
    features = VectorField(dim=512, null=True)
    source_type = fields.CharEnumField(ContentSourceType)
    business_type = fields.CharEnumField(BusinessType, null=True)
    process_status = fields.CharEnumField(ProcessStatus, default=ProcessStatus.PENDING)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    processed_at = fields.DatetimeField(null=True)
    error_message = fields.TextField(null=True)
    metadata = fields.JSONField(null=True)
    
    class Meta:
        table = "images"
```

### Videos模型
```python
class Videos(Model):
    id = fields.UUIDField(pk=True)
    path = fields.CharField(max_length=1000)
    frame_time = fields.FloatField()
    frame_index = fields.IntField()
    features = VectorField(dim=512, null=True)
    duration = fields.FloatField(null=True)
    fps = fields.FloatField(null=True)
    resolution = fields.CharField(max_length=20, null=True)
    source_type = fields.CharEnumField(ContentSourceType)
    process_status = fields.CharEnumField(ProcessStatus, default=ProcessStatus.PENDING)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    metadata = fields.JSONField(null=True)
    
    class Meta:
        table = "videos"
```

### Tasks模型
```python
class Tasks(Model):
    id = fields.UUIDField(pk=True)
    task_type = fields.CharEnumField(TaskType)
    status = fields.CharEnumField(TaskStatus, default=TaskStatus.PENDING)
    total_files = fields.IntField(default=0)
    processed_files = fields.IntField(default=0)
    failed_files = fields.IntField(default=0)
    progress = fields.FloatField(default=0.0)
    started_at = fields.DatetimeField(null=True)
    completed_at = fields.DatetimeField(null=True)
    locked_at = fields.DatetimeField(null=True)
    locked_by = fields.CharField(max_length=100, null=True)
    retry_count = fields.IntField(default=0)
    max_retries = fields.IntField(default=3)
    created_by = fields.CharField(max_length=100, null=True)
    metadata = fields.JSONField(null=True)
    
    class Meta:
        table = "tasks"
```

## 向量索引优化

### IVFFlat索引
- **适用场景**：中等规模数据集（1万-100万向量）
- **配置示例**：
```sql
CREATE INDEX idx_images_features_ivfflat 
ON images USING ivfflat(features vector_cosine_ops) 
WITH (lists = 100);
```

### HNSW索引
- **适用场景**：大规模数据集（100万+向量）
- **配置示例**：
```sql
CREATE INDEX idx_images_features_hnsw 
ON images USING hnsw(features vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);
```

## JSON属性查询技术

### PostgreSQL JSONB操作符
- **@>**：包含操作符
- **?**：键存在操作符
- **?&**：所有键存在操作符
- **?|**：任意键存在操作符
- **#>**：JSON路径操作符
- **#>>**：JSON路径文本操作符

### 查询示例
```sql
-- 精确匹配
SELECT * FROM images WHERE metadata @> '{"order_id": "ORD-001"}';

-- 键存在查询
SELECT * FROM images WHERE metadata ? 'category';

-- 数组包含
SELECT * FROM images WHERE metadata @> '{"tags": ["产品"]}';

-- 范围查询
SELECT * FROM images WHERE (metadata->>'price')::numeric BETWEEN 100 AND 500;

-- JSON路径查询
SELECT * FROM images WHERE metadata #>> '{order,customer,name}' = '张三';
```

## 数据库连接配置

### 连接池配置
```python
DATABASE_CONFIG = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.asyncpg",
            "credentials": {
                "host": os.getenv("DB_HOST", "localhost"),
                "port": int(os.getenv("DB_PORT", "5432")),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASSWORD", ""),
                "database": os.getenv("DB_NAME", "materialsearch"),
                "minsize": int(os.getenv("DB_POOL_MIN_SIZE", "1")),
                "maxsize": int(os.getenv("DB_POOL_MAX_SIZE", "20")),
            }
        }
    },
    "apps": {
        "models": {
            "models": ["app.models.database"],
            "default_connection": "default",
        }
    }
}
```

## 性能优化建议

### 1. 索引优化
- 为常用查询字段建立B-tree索引
- 使用GIN索引优化JSONB查询
- 合理配置向量索引参数

### 2. 查询优化
- 使用适当的向量相似度阈值
- 限制查询结果数量（LIMIT）
- 避免全表扫描

### 3. 连接优化
- 合理设置连接池大小
- 使用异步连接池
- 监控连接使用情况

### 4. 维护优化
- 定期执行VACUUM和ANALYZE
- 监控表和索引大小
- 定期重建向量索引

## 数据迁移

### 初始化脚本
```python
from tortoise import Tortoise

async def init_db():
    await Tortoise.init(config=DATABASE_CONFIG)
    await Tortoise.generate_schemas()

async def create_vector_extension():
    conn = Tortoise.get_connection("default")
    await conn.execute_query("CREATE EXTENSION IF NOT EXISTS vector;")
```

### 数据备份策略
- **全量备份**：定期备份整个数据库
- **增量备份**：备份WAL日志
- **向量数据备份**：单独备份特征向量表 