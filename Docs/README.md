# MaterialSearch 项目文档

基于AI的本地媒体搜索系统技术文档集合

## 文档结构

### 核心文档
- [项目概述](./01-项目概述.md) - 项目介绍、功能特性、技术选型
- [系统架构](./02-系统架构.md) - 整体架构设计、核心模块说明
- [数据库设计](./03-数据库设计.md) - PostgreSQL + pgvector + Tortoise ORM设计
- [API接口设计](./04-API接口设计.md) - 统一响应格式、接口规范、错误处理

### 功能设计
- [扩展属性系统](./05-扩展属性系统.md) - 灵活的JSON元数据管理
- [任务处理系统](./06-任务处理系统.md) - 同步异步处理、防重复机制
- [模型管理系统](./07-模型管理系统.md) - 多后端支持、动态切换
- [日志管理系统](./08-日志管理系统.md) - 统一日志管理、分级输出

### 性能优化
- [搜索性能优化](./09-搜索性能优化.md) - 快速匹配、缓存策略、批量处理
- [系统性能调优](./10-系统性能调优.md) - 硬件配置、软件优化、监控告警

### 部署运维
- [部署指南](./11-部署指南.md) - 源码部署、Docker部署
- [配置管理](./12-配置管理.md) - 环境变量、配置参数说明

### 开发指南
- [FastAPI迁移指南](./13-FastAPI迁移指南.md) - 从Flask到FastAPI的迁移方案

## 快速开始

1. 阅读 [项目概述](./01-项目概述.md) 了解项目基本情况
2. 查看 [系统架构](./02-系统架构.md) 理解整体设计
3. 参考 [配置管理](./12-配置管理.md) 了解配置参数
4. 按照 [部署指南](./11-部署指南.md) 进行环境搭建
5. 根据需要查阅具体功能模块的详细文档

## 实际可用文档

目前已完成的文档：
- ✅ [项目概述](./01-项目概述.md)
- ✅ [系统架构](./02-系统架构.md)
- ✅ [数据库设计](./03-数据库设计.md)
- ✅ [API接口设计](./04-API接口设计.md)
- ✅ [扩展属性系统](./05-扩展属性系统.md)
- ✅ [任务处理系统](./06-任务处理系统.md)
- ✅ [模型管理系统](./07-模型管理系统.md)
- ✅ [日志管理系统](./08-日志管理系统.md)
- ✅ [搜索性能优化](./09-搜索性能优化.md)
- ✅ [系统性能调优](./10-系统性能调优.md)
- ✅ [部署指南](./11-部署指南.md)
- ✅ [配置管理](./12-配置管理.md)

待完成的文档：
- ⏳ [FastAPI迁移指南](./13-FastAPI迁移指南.md)
- ⏳ [运维监控](./14-运维监控.md)
- ⏳ [开发规范](./15-开发规范.md)

## 文档维护

- 文档版本：v2.0
- 最后更新：2024-12-31
- 维护者：开发团队
- 拆分状态：已完成核心文档拆分，剩余文档可根据需要继续完善

## 贡献指南

欢迎提交文档改进建议和补充内容，请遵循以下原则：
- 保持文档结构清晰
- 内容准确且易于理解
- 及时更新过时信息
- 添加必要的示例和说明
