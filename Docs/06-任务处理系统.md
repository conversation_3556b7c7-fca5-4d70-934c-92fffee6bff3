# 任务处理系统设计

## 概述

任务处理系统支持同步和异步两种处理模式，使用FastAPI的BackgroundTasks和数据库任务表来管理任务，确保多进程部署时不会重复处理任务。

## 同步异步处理架构

### 处理模式选择
- **同步处理**：直接在请求线程中处理，适用于小文件和实时需求
- **异步处理**：使用FastAPI BackgroundTasks，适用于大文件和批量操作
- **混合模式**：根据文件大小和数量自动选择处理模式

### FastAPI BackgroundTasks优势
- **轻量级**：无需额外的消息队列组件
- **进程内**：与主应用共享内存和数据库连接
- **简单可靠**：自动处理异常和资源清理
- **扩展性**：支持多进程部署时的任务分发

## 多进程部署任务防重复机制

### 任务锁定策略
- **乐观锁**：使用数据库行级锁防止重复处理
- **进程标识**：记录处理进程ID和锁定时间
- **超时释放**：锁定超时自动释放，防止死锁
- **状态原子更新**：使用数据库事务确保状态更新原子性

### 启动时任务恢复
- **扫描未完成任务**：启动时查询PENDING和LOCKED状态的任务
- **锁定超时检查**：检查锁定时间是否超时
- **进程存活检查**：验证锁定进程是否仍在运行
- **任务重新分配**：将超时或进程不存在的任务重新分配

## 任务处理流程

### 同步处理流程
- **接收请求**：验证文件和参数
- **直接处理**：在请求线程中提取特征
- **数据库更新**：保存特征向量和元数据
- **返回结果**：直接返回处理结果

### 异步处理流程
- **创建任务**：在tasks表中创建任务记录
- **后台处理**：使用BackgroundTasks启动处理
- **任务锁定**：原子性更新任务状态为RUNNING
- **特征提取**：处理文件并提取特征
- **结果保存**：更新数据库和任务状态
- **状态通知**：通过WebSocket推送状态更新

### 任务恢复流程
- **启动扫描**：应用启动时扫描tasks表
- **锁定检查**：检查LOCKED状态任务的超时情况
- **进程验证**：验证locked_by进程是否存在
- **任务重置**：将超时任务状态重置为PENDING
- **重新处理**：将PENDING任务加入处理队列

## 任务状态管理

### 任务状态枚举
- **PENDING**: 等待开始
- **RUNNING**: 正在执行
- **COMPLETED**: 执行完成
- **FAILED**: 执行失败
- **CANCELLED**: 已取消
- **LOCKED**: 已锁定（防止多进程重复处理）

### 任务类型枚举
- **SINGLE_FILE**: 单文件处理
- **BATCH_FILES**: 批量文件处理
- **DIRECTORY_SCAN**: 目录扫描

## API接口设计

### 单文件添加
```python
@router.post("/content/add-file", response_model=ApiResponse[AddFileResultData])
async def add_file(
    request: AddFileRequest,
    async_mode: bool = False
) -> ApiResponse[AddFileResultData]:
    pass
```
- 支持文件上传或文件路径，扩展属性参数（任意JSON结构）
- 查询参数：`async=true/false`（默认false同步处理）
- 返回：`ApiResponse[AddFileResultData]`格式

### 批量文件添加
```python
@router.post("/content/add-batch", response_model=ApiResponse[BatchTaskData])
async def add_batch_files(
    request: AddBatchRequest
) -> ApiResponse[BatchTaskData]:
    pass
```
- 支持多文件上传或路径列表，为每个文件指定不同的扩展属性
- 默认异步处理，返回：`ApiResponse[BatchTaskData]`格式

### 同步vs异步处理策略
- **同步处理**：适用于单文件、小文件、实时性要求高的场景
- **异步处理**：适用于批量文件、大文件、可容忍延迟的场景
- **自动降级**：同步处理超时自动转为异步处理

### 任务状态管理
```python
@router.get("/tasks/{task_id}", response_model=ApiResponse[TaskDetailData])
async def get_task_status(task_id: str) -> ApiResponse[TaskDetailData]:
    pass

@router.get("/tasks", response_model=ApiResponse[TaskListData])
async def get_task_list(
    page: int = 1, 
    size: int = 20
) -> ApiResponse[TaskListData]:
    pass

@router.post("/tasks/{task_id}/cancel", response_model=ApiResponse[None])
async def cancel_task(task_id: str) -> ApiResponse[None]:
    pass
```
- 支持任务查询、取消、重试、删除等操作
- 所有接口都使用统一的`ApiResponse[T]`格式

## 技术实现细节

### 任务锁定机制
使用数据库UPDATE...WHERE...RETURNING语句实现原子性任务锁定，防止多进程重复处理。

### 启动时任务恢复
查询PENDING和LOCKED状态的任务，检查锁定超时和进程存活状态，重新分配需要恢复的任务。

### 进程标识生成
使用进程ID+时间戳、主机名+进程ID或UUID等方案确保多进程环境下的唯一标识。

## 性能优化

### 任务处理优化
- **批量处理**：合并小任务减少数据库操作，参考Demo的批量机制
- **连接池**：复用数据库连接减少开销
- **内存管理**：及时释放处理完成的文件内存，避免内存泄漏
- **错误隔离**：单个文件失败不影响批量任务
- **智能调度**：根据系统负载动态调整任务并发数
- **优先级队列**：实时添加任务优先于批量扫描任务

## 配置参数

### 任务处理配置
- **SYNC_PROCESS_TIMEOUT**: 同步处理超时时间（秒）
- **TASK_LOCK_TIMEOUT**: 任务锁定超时时间（分钟）
- **MAX_RETRY_ATTEMPTS**: 最大重试次数
- **TASK_CLEANUP_INTERVAL**: 任务清理间隔（小时）
- **BACKGROUND_TASK_WORKERS**: 后台任务工作线程数

### 实时添加配置
- **REALTIME_PROCESS_ENABLED**: 是否启用实时处理
- **MAX_CONCURRENT_UPLOADS**: 最大并发上传数
- **UPLOAD_TIMEOUT**: 上传超时时间
- **AUTO_ASYNC_THRESHOLD**: 自动转异步的文件大小阈值

## 监控和维护

### 任务监控
- **任务状态统计**：各状态任务数量统计
- **处理性能监控**：任务处理时间和成功率
- **资源使用监控**：内存和CPU使用情况
- **错误率监控**：失败任务比例和错误类型

### 维护操作
- **任务清理**：定期清理完成的任务记录
- **失败重试**：自动或手动重试失败的任务
- **状态修复**：修复异常状态的任务
- **性能调优**：根据监控数据调整配置参数
