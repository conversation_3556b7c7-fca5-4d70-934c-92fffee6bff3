# MaterialSearch API接口设计

## 统一响应格式

### 泛型响应模型设计

#### 响应格式定义
```python
from typing import TypeVar, Generic, Optional
from pydantic import BaseModel

T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    ErrorCode: int = 0
    Msg: str = "操作成功"
    IsSuccess: bool = True
    Body: Optional[T] = None
```

#### 具体业务响应模型
```python
class SearchResultData(BaseModel):
    results: List[SearchResult]
    total: int
    query_time: float

class TaskStatusData(BaseModel):
    task_id: str
    status: str
    progress: float

class AddFileResultData(BaseModel):
    file_id: str
    path: str
    status: str
    process_time: Optional[float] = None

# 类型别名定义
SearchResponse = ApiResponse[SearchResultData]
TaskResponse = ApiResponse[TaskStatusData]
AddFileResponse = ApiResponse[AddFileResultData]
```

#### 成功响应示例
```json
{
  "ErrorCode": 0,
  "Msg": "搜索完成",
  "IsSuccess": true,
  "Body": {
    "results": [...],
    "total": 10,
    "query_time": 0.15
  }
}
```

#### 错误响应示例
```json
{
  "ErrorCode": 2001,
  "Msg": "文件格式不支持",
  "IsSuccess": false,
  "Body": null
}
```

## 错误码体系

### 错误码分类
- **0**: 操作成功
- **-1**: 通用错误
- **1001-1099**: 参数验证错误
- **2001-2099**: 文件处理错误
- **3001-3099**: 数据库操作错误
- **4001-4099**: 模型推理错误
- **5001-5099**: 任务处理错误


### 具体错误码定义
```python
class ErrorCode:
    SUCCESS = 0
    GENERAL_ERROR = -1
    
    # 参数错误 1001-1099
    INVALID_PARAMETER = 1001
    MISSING_PARAMETER = 1002
    PARAMETER_TYPE_ERROR = 1003
    PARAMETER_RANGE_ERROR = 1004
    
    # 文件错误 2001-2099
    FILE_NOT_FOUND = 2001
    FILE_FORMAT_UNSUPPORTED = 2002
    FILE_SIZE_EXCEEDED = 2003
    FILE_UPLOAD_FAILED = 2004
    FILE_PROCESSING_FAILED = 2005
    
    # 数据库错误 3001-3099
    DATABASE_CONNECTION_ERROR = 3001
    DATABASE_QUERY_ERROR = 3002
    DATABASE_INSERT_ERROR = 3003
    DATABASE_UPDATE_ERROR = 3004
    DATABASE_DELETE_ERROR = 3005
    
    # 模型错误 4001-4099
    MODEL_LOAD_FAILED = 4001
    MODEL_INFERENCE_FAILED = 4002
    MODEL_NOT_FOUND = 4003
    MODEL_SWITCH_FAILED = 4004
    
    # 任务错误 5001-5099
    TASK_NOT_FOUND = 5001
    TASK_CREATE_FAILED = 5002
    TASK_CANCEL_FAILED = 5003
    TASK_TIMEOUT = 5004
    
    # 权限错误 6001-6099
    UNAUTHORIZED = 6001
    FORBIDDEN = 6002
    TOKEN_EXPIRED = 6003
    INVALID_TOKEN = 6004
```

## 核心API接口

### 1. 搜索接口

#### 文字搜图
```python
@router.post("/search/images", response_model=ApiResponse[SearchResultData])
async def search_images_by_text(
    request: TextSearchRequest
) -> ApiResponse[SearchResultData]:
    """
    通过文字描述搜索图片
    
    Args:
        request: 搜索请求参数
            - query: 搜索文字
            - limit: 结果数量限制
            - threshold: 相似度阈值
            - filters: 扩展属性过滤条件
    
    Returns:
        包含搜索结果的ApiResponse
    """
    pass

class TextSearchRequest(BaseModel):
    query: str = Field(..., description="搜索查询文字")
    limit: int = Field(20, ge=1, le=100, description="返回结果数量")
    threshold: float = Field(0.8, ge=0, le=1, description="相似度阈值")
    filters: Optional[Dict[str, Any]] = Field(None, description="扩展属性过滤")
```

#### 以图搜图
```python
@router.post("/search/images-by-image", response_model=ApiResponse[SearchResultData])
async def search_images_by_image(
    image_file: Optional[UploadFile] = File(None),
    image_path: Optional[str] = Form(None),
    limit: int = Form(20),
    threshold: float = Form(0.85)
) -> ApiResponse[SearchResultData]:
    """
    通过图片搜索相似图片
    
    Args:
        image_file: 上传的图片文件
        image_path: 已存在的图片路径
        limit: 结果数量限制
        threshold: 相似度阈值
    
    Returns:
        包含搜索结果的ApiResponse
    """
    pass
```

#### 组合搜索
```python
@router.post("/search/combined", response_model=ApiResponse[SearchResultData])
async def search_combined(
    request: CombinedSearchRequest
) -> ApiResponse[SearchResultData]:
    """
    视觉相似度与属性条件组合搜索
    
    Args:
        request: 组合搜索请求参数
    
    Returns:
        包含搜索结果的ApiResponse
    """
    pass

class CombinedSearchRequest(BaseModel):
    # 视觉搜索部分
    query_text: Optional[str] = None
    query_image: Optional[str] = None  # 图片路径或base64
    visual_weight: float = Field(0.7, ge=0, le=1)
    
    # 属性搜索部分
    attribute_filters: Dict[str, Any] = Field(default_factory=dict)
    attribute_weight: float = Field(0.3, ge=0, le=1)
    
    # 通用参数
    limit: int = Field(20, ge=1, le=100)
    threshold: float = Field(0.8, ge=0, le=1)
```

### 2. 内容管理接口

#### 实时添加文件
```python
@router.post("/content/add-file", response_model=ApiResponse[AddFileResultData])
async def add_file(
    file: Optional[UploadFile] = File(None),
    file_path: Optional[str] = Form(None),
    metadata: Optional[str] = Form(None),  # JSON字符串
    business_type: Optional[str] = Form(None),
    async_mode: bool = Form(False)
) -> ApiResponse[AddFileResultData]:
    """
    实时添加单个文件到索引
    
    Args:
        file: 上传的文件
        file_path: 本地文件路径
        metadata: 扩展属性JSON字符串
        business_type: 业务类型
        async_mode: 是否异步处理
    
    Returns:
        包含处理结果的ApiResponse
    """
    pass
```

#### 批量添加文件
```python
@router.post("/content/add-batch", response_model=ApiResponse[BatchTaskData])
async def add_batch_files(
    request: AddBatchRequest
) -> ApiResponse[BatchTaskData]:
    """
    批量添加文件到索引
    
    Args:
        request: 批量添加请求参数
    
    Returns:
        包含任务信息的ApiResponse
    """
    pass

class AddBatchRequest(BaseModel):
    files: List[FileAddRequest] = Field(..., description="文件列表")
    business_type: Optional[str] = Field(None, description="默认业务类型")

class FileAddRequest(BaseModel):
    file_path: str = Field(..., description="文件路径")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")
    business_type: Optional[str] = Field(None, description="业务类型")
```

#### 内容列表查询
```python
@router.get("/content/list", response_model=ApiResponse[ContentListData])
async def get_content_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    content_type: Optional[str] = Query(None, description="内容类型"),
    source_type: Optional[str] = Query(None, description="来源类型"),
    business_type: Optional[str] = Query(None, description="业务类型"),
    process_status: Optional[str] = Query(None, description="处理状态")
) -> ApiResponse[ContentListData]:
    """
    获取内容列表
    
    Returns:
        包含内容列表的ApiResponse
    """
    pass
```

#### 更新内容元数据
```python
@router.patch("/content/{file_id}/metadata", response_model=ApiResponse[None])
async def update_metadata(
    file_id: str,
    request: UpdateMetadataRequest
) -> ApiResponse[None]:
    """
    更新文件的扩展属性
    
    Args:
        file_id: 文件ID
        request: 更新请求参数
    
    Returns:
        操作结果的ApiResponse
    """
    pass

class UpdateMetadataRequest(BaseModel):
    metadata: Dict[str, Any] = Field(..., description="新的扩展属性")
    merge: bool = Field(True, description="是否与现有属性合并")
```

### 3. 任务管理接口

#### 任务状态查询
```python
@router.get("/tasks/{task_id}", response_model=ApiResponse[TaskDetailData])
async def get_task_status(task_id: str) -> ApiResponse[TaskDetailData]:
    """
    获取任务详细状态
    
    Args:
        task_id: 任务ID
    
    Returns:
        包含任务详情的ApiResponse
    """
    pass

class TaskDetailData(BaseModel):
    task_id: str
    task_type: str
    status: str
    progress: float
    total_files: int
    processed_files: int
    failed_files: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    metadata: Optional[Dict[str, Any]]
```

#### 任务列表查询
```python
@router.get("/tasks", response_model=ApiResponse[TaskListData])
async def get_task_list(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    task_type: Optional[str] = Query(None)
) -> ApiResponse[TaskListData]:
    """
    获取任务列表
    
    Returns:
        包含任务列表的ApiResponse
    """
    pass
```

#### 取消任务
```python
@router.post("/tasks/{task_id}/cancel", response_model=ApiResponse[None])
async def cancel_task(task_id: str) -> ApiResponse[None]:
    """
    取消指定任务
    
    Args:
        task_id: 任务ID
    
    Returns:
        操作结果的ApiResponse
    """
    pass
```

### 4. 模型管理接口

#### 获取模型信息
```python
@router.get("/models/current", response_model=ApiResponse[ModelInfoData])
async def get_current_model() -> ApiResponse[ModelInfoData]:
    """
    获取当前使用的模型信息
    
    Returns:
        包含模型信息的ApiResponse
    """
    pass

class ModelInfoData(BaseModel):
    model_name: str
    backend: str
    device: str
    loaded_at: datetime
    inference_count: int
    avg_inference_time: float
```

#### 切换模型
```python
@router.post("/models/switch", response_model=ApiResponse[None])
async def switch_model(
    request: SwitchModelRequest
) -> ApiResponse[None]:
    """
    切换CLIP模型
    
    Args:
        request: 模型切换请求参数
    
    Returns:
        操作结果的ApiResponse
    """
    pass

class SwitchModelRequest(BaseModel):
    model_name: str = Field(..., description="模型名称")
    backend: str = Field("transformers", description="推理后端")
    device: Optional[str] = Field(None, description="推理设备")
```

## 请求验证

### Pydantic模型验证
```python
class SearchResult(BaseModel):
    file_id: str
    path: str
    filename: str
    similarity_score: float
    file_type: str
    file_size: int
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None

class SearchResultData(BaseModel):
    results: List[SearchResult] = Field(..., description="搜索结果列表")
    total: int = Field(..., ge=0, description="总结果数")
    query_time: float = Field(..., ge=0, description="查询耗时(秒)")
    
    class Config:
        schema_extra = {
            "example": {
                "results": [
                    {
                        "file_id": "uuid-string",
                        "path": "/path/to/image.jpg",
                        "filename": "image.jpg",
                        "similarity_score": 0.95,
                        "file_type": "image",
                        "file_size": 1024000,
                        "created_at": "2024-01-01T12:00:00Z",
                        "metadata": {"order_id": "ORD-001"}
                    }
                ],
                "total": 1,
                "query_time": 0.15
            }
        }
```

## 异常处理机制

### 全局异常处理器
```python
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=200,  # 业务错误也返回200
        content=ApiResponse(
            ErrorCode=exc.status_code,
            Msg=exc.detail,
            IsSuccess=False,
            Body=None
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=200,
        content=ApiResponse(
            ErrorCode=ErrorCode.GENERAL_ERROR,
            Msg="系统内部错误",
            IsSuccess=False,
            Body=None
        ).dict()
    )
```

### 自定义异常类
```python
class BusinessException(Exception):
    def __init__(self, error_code: int, message: str):
        self.error_code = error_code
        self.message = message
        super().__init__(message)

class FileProcessingException(BusinessException):
    def __init__(self, message: str = "文件处理失败"):
        super().__init__(ErrorCode.FILE_PROCESSING_FAILED, message)

class ModelInferenceException(BusinessException):
    def __init__(self, message: str = "模型推理失败"):
        super().__init__(ErrorCode.MODEL_INFERENCE_FAILED, message)
```

## API文档配置

### Swagger配置
```python
app = FastAPI(
    title="MaterialSearch API",
    description="基于AI的本地媒体搜索系统API",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# 添加标签元数据
tags_metadata = [
    {
        "name": "search",
        "description": "搜索相关接口",
    },
    {
        "name": "content",
        "description": "内容管理接口",
    },
    {
        "name": "tasks",
        "description": "任务管理接口",
    },
    {
        "name": "models",
        "description": "模型管理接口",
    }
]

app.openapi_tags = tags_metadata
```

## 接口性能监控

### 请求耗时记录
```python
import time
from fastapi import Request

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # 记录慢查询
    if process_time > 1.0:
        logger.warning(f"Slow request: {request.url} took {process_time:.2f}s")
    
    return response
``` 