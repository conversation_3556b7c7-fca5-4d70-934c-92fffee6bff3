# 快速开始指南

## 概述

本指南帮助您快速部署和使用MaterialSearch系统。系统采用简化设计，专注于核心的AI搜索功能，避免过度复杂的配置和权限管理。

## 环境要求

### 最低配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB可用空间
- **Python**: 3.9+
- **PostgreSQL**: 14+

### 推荐配置
- **CPU**: 8核心以上
- **内存**: 16GB RAM以上
- **存储**: SSD，100GB以上
- **GPU**: 8GB显存以上（可选，用于加速）

## 快速部署

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd MaterialSearch

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置
```bash
# 安装PostgreSQL和pgvector扩展
sudo apt-get install postgresql postgresql-contrib
sudo -u postgres psql -c "CREATE EXTENSION vector;"

# 创建数据库
sudo -u postgres createdb materialsearch
sudo -u postgres psql -c "CREATE USER materialsearch WITH PASSWORD 'your_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE materialsearch TO materialsearch;"
```

### 3. 基础配置
创建 `config/development.yaml`:
```yaml
server:
  host: "127.0.0.1"
  port: 8000
  debug: true

database:
  host: "localhost"
  port: 5432
  name: "materialsearch"
  user: "materialsearch"
  password: "your_password"
  pool_size: 10

model:
  name: "OFA-Sys/chinese-clip-vit-base-patch16"
  backend: "transformers"
  device: "auto"
  batch_size: 4

scan:
  assets_paths:
    - "./test_data"
  frame_interval: 2

logging:
  level: "INFO"
  console_enabled: true
  file_enabled: false
```

### 4. 启动服务
```bash
# 设置环境变量
export ENVIRONMENT=development

# 初始化数据库
python -m app.init_db

# 启动服务
python -m app.main
```

### 5. 访问系统
打开浏览器访问：http://localhost:8000

## 基本使用

### 文件扫描
1. 将图片/视频文件放入 `test_data` 目录
2. 在Web界面点击"开始扫描"
3. 等待扫描完成

### 搜索功能
1. **文字搜索**：在搜索框输入描述，如"红色的花"
2. **以图搜图**：上传图片或拖拽到搜索区域
3. **查看结果**：点击结果图片查看详情

### 实时添加
1. 点击"上传文件"按钮
2. 选择图片或视频文件
3. 可选：添加扩展属性（如订单ID、标签等）
4. 点击"上传并处理"

## Docker部署

### 1. 使用Docker Compose
创建 `docker-compose.yml`:
```yaml
version: '3.8'
services:
  materialsearch:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    volumes:
      - ./config:/app/config
      - ./data:/data
      - ./logs:/app/logs
    depends_on:
      - postgres

  postgres:
    image: pgvector/pgvector:pg14
    environment:
      - POSTGRES_DB=materialsearch
      - POSTGRES_USER=materialsearch
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### 2. 启动服务
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f materialsearch
```

## 性能优化

### GPU加速
如果有GPU，修改配置：
```yaml
model:
  device: "cuda"
  batch_size: 8  # 根据显存调整
```

### 批量处理优化
```yaml
model:
  batch_size: 16  # 大显存可设置更大值

scan:
  auto_save_interval: 50  # 减少保存频率
```

### 缓存优化
```yaml
search:
  cache_size: 128  # 增加缓存大小
```

## 常见问题

### 1. 模型下载慢
```bash
# 设置镜像源
export HF_ENDPOINT=https://hf-mirror.com
```

### 2. GPU内存不足
```yaml
model:
  batch_size: 2  # 减少批量大小
  precision: "fp16"  # 使用半精度
```

### 3. 搜索结果不准确
```yaml
search:
  positive_threshold: 30  # 降低阈值
  image_threshold: 80     # 调整图片阈值
```

### 4. 扫描速度慢
- 检查是否使用SSD存储
- 确认GPU正常工作
- 调整批量处理大小

## 监控和维护

### 系统状态
访问：http://localhost:8000/api/system/status

### 日志查看
```bash
# 实时日志
tail -f logs/app.log

# 错误日志
grep "ERROR" logs/app.log
```

### 数据库维护
```bash
# 连接数据库
psql -h localhost -U materialsearch -d materialsearch

# 查看表大小
SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size 
FROM pg_tables WHERE schemaname='public';
```

## 扩展配置

### 支持更多文件格式
```yaml
scan:
  image_extensions:
    - ".jpg"
    - ".png"
    - ".webp"
    - ".tiff"
  video_extensions:
    - ".mp4"
    - ".avi"
    - ".mkv"
    - ".webm"
```

### 多目录扫描
```yaml
scan:
  assets_paths:
    - "/data/images"
    - "/data/videos"
    - "/backup/media"
  skip_paths:
    - "/data/temp"
    - "/data/cache"
```

### 自定义阈值
```yaml
search:
  positive_threshold: 36
  negative_threshold: 36
  image_threshold: 85
```

## 备份和恢复

### 数据库备份
```bash
pg_dump -h localhost -U materialsearch materialsearch > backup.sql
```

### 数据库恢复
```bash
psql -h localhost -U materialsearch materialsearch < backup.sql
```

### 配置备份
```bash
cp -r config config_backup_$(date +%Y%m%d)
```

这个快速开始指南专注于核心功能，避免了复杂的权限配置和过度设计，让用户能够快速上手使用系统。
