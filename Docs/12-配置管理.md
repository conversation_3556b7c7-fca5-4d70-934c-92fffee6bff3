# 配置管理

## 概述

系统支持通过环境变量或`.env`文件进行灵活配置，涵盖服务器、数据库、模型、搜索、任务处理、日志等各个方面的配置参数。

## 配置参数详解

### 服务器配置
- **HOST**: 监听IP地址（默认：127.0.0.1）
- **PORT**: 监听端口（默认：8085）
- **DEBUG**: 调试模式（默认：False）

### 扫描配置
- **ASSETS_PATH**: 扫描路径，逗号分隔
- **SKIP_PATH**: 跳过路径，逗号分隔
- **IMAGE_EXTENSIONS**: 支持的图片格式
- **VIDEO_EXTENSIONS**: 支持的视频格式
- **FRAME_INTERVAL**: 视频帧提取间隔（秒）
- **IMAGE_MIN_WIDTH/HEIGHT**: 图片最小尺寸

### 模型配置
- **MODEL_NAME**: CLIP模型名称（支持Hugging Face模型ID或本地路径）
- **MODEL_BACKEND**: 推理后端（transformers/onnx/openvino）
- **MODEL_PRECISION**: 模型精度（fp32/fp16/int8）
- **DEVICE**: 推理设备（auto/cpu/cuda/mps）
- **SCAN_PROCESS_BATCH_SIZE**: 批处理大小
- **MODEL_CACHE_DIR**: 模型缓存目录
- **ONNX_MODEL_PATH**: ONNX模型文件路径（当backend为onnx时）
- **OPENVINO_MODEL_PATH**: OpenVINO模型文件路径（当backend为openvino时）

### 数据库配置
- **DATABASE_URL**: PostgreSQL连接字符串
- **DB_HOST**: 数据库主机地址
- **DB_PORT**: 数据库端口（默认5432）
- **DB_NAME**: 数据库名称
- **DB_USER**: 数据库用户名
- **DB_PASSWORD**: 数据库密码
- **DB_POOL_SIZE**: 连接池大小
- **VECTOR_DIMENSION**: 特征向量维度（默认512）

### 搜索配置
- **CACHE_SIZE**: 搜索缓存大小（默认64，LRU缓存条目数）
- **POSITIVE_THRESHOLD**: 正向搜索阈值（默认36，0-100）
- **NEGATIVE_THRESHOLD**: 反向搜索阈值（默认36，0-100）
- **IMAGE_THRESHOLD**: 图片搜索阈值（默认85，0-100）

### 性能优化配置
- **SCAN_PROCESS_BATCH_SIZE**: 批处理大小（默认4，影响GPU利用率和内存使用）
- **FRAME_INTERVAL**: 视频帧提取间隔（默认2秒，影响视频处理速度）
- **IMAGE_MIN_WIDTH/HEIGHT**: 图片最小尺寸（默认64px，过滤小图片提升性能）
- **AUTO_SAVE_INTERVAL**: 扫描自动保存间隔（默认100个文件）

### 任务处理配置
- **SYNC_PROCESS_TIMEOUT**: 同步处理超时时间（秒）
- **TASK_LOCK_TIMEOUT**: 任务锁定超时时间（分钟）
- **MAX_RETRY_ATTEMPTS**: 最大重试次数
- **TASK_CLEANUP_INTERVAL**: 任务清理间隔（小时）
- **BACKGROUND_TASK_WORKERS**: 后台任务工作线程数

### 日志配置
- **LOG_LEVEL**: 日志级别（DEBUG/INFO/WARNING/ERROR/CRITICAL）
- **LOG_FORMAT**: 日志格式（json/text）
- **LOG_FILE_PATH**: 日志文件路径
- **LOG_MAX_SIZE**: 单个日志文件最大大小（MB）
- **LOG_BACKUP_COUNT**: 日志文件备份数量
- **LOG_ROTATION**: 日志轮转方式（time/size）
- **ENABLE_CONSOLE_LOG**: 是否启用控制台日志输出
- **ENABLE_FILE_LOG**: 是否启用文件日志输出

### 实时添加配置
- **REALTIME_PROCESS_ENABLED**: 是否启用实时处理
- **MAX_CONCURRENT_UPLOADS**: 最大并发上传数
- **UPLOAD_TIMEOUT**: 上传超时时间
- **AUTO_ASYNC_THRESHOLD**: 自动转异步的文件大小阈值

### 认证配置
- **ENABLE_LOGIN**: 是否启用登录验证
- **SECRET_KEY**: JWT密钥
- **ACCESS_TOKEN_EXPIRE_MINUTES**: 访问令牌过期时间（分钟）

## 配置文件示例

### .env文件示例
```bash
# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=False

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/materialsearch
DB_POOL_SIZE=20
VECTOR_DIMENSION=512

# 模型配置
MODEL_NAME=OFA-Sys/chinese-clip-vit-base-patch16
MODEL_BACKEND=transformers
MODEL_PRECISION=fp32
DEVICE=auto
SCAN_PROCESS_BATCH_SIZE=4

# 扫描配置
ASSETS_PATH=/data/images,/data/videos
SKIP_PATH=/tmp,/var/cache
FRAME_INTERVAL=2
IMAGE_MIN_WIDTH=64
IMAGE_MIN_HEIGHT=64

# 搜索配置
CACHE_SIZE=64
POSITIVE_THRESHOLD=36
NEGATIVE_THRESHOLD=36
IMAGE_THRESHOLD=85

# 任务处理配置
SYNC_PROCESS_TIMEOUT=30
TASK_LOCK_TIMEOUT=30
MAX_RETRY_ATTEMPTS=3
BACKGROUND_TASK_WORKERS=4

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/materialsearch/app.log
LOG_MAX_SIZE=100
LOG_BACKUP_COUNT=5
LOG_ROTATION=size
ENABLE_CONSOLE_LOG=True
ENABLE_FILE_LOG=True

# 实时添加配置
REALTIME_PROCESS_ENABLED=True
MAX_CONCURRENT_UPLOADS=10
UPLOAD_TIMEOUT=300
AUTO_ASYNC_THRESHOLD=10485760

# 认证配置
ENABLE_LOGIN=False
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 配置管理最佳实践

### 环境分离
- **开发环境**：使用本地配置，启用调试模式
- **测试环境**：使用测试数据库，启用详细日志
- **生产环境**：使用生产配置，优化性能参数

### 敏感信息管理
- **密钥管理**：使用环境变量或密钥管理服务
- **数据库密码**：避免在代码中硬编码
- **API密钥**：使用安全的密钥存储方案
- **证书文件**：使用安全的文件权限

### 配置验证
- **参数验证**：启动时验证配置参数的有效性
- **依赖检查**：检查必需的外部依赖是否可用
- **兼容性检查**：检查配置参数之间的兼容性
- **默认值**：为所有配置参数提供合理的默认值

### 配置热更新
- **动态配置**：支持部分配置的热更新
- **配置监控**：监控配置文件的变更
- **安全重载**：确保配置更新的安全性
- **回滚机制**：配置更新失败时的回滚机制

## 不同部署场景的配置建议

### 开发环境配置
```bash
DEBUG=True
LOG_LEVEL=DEBUG
ENABLE_CONSOLE_LOG=True
CACHE_SIZE=16
SCAN_PROCESS_BATCH_SIZE=2
```

### 测试环境配置
```bash
DEBUG=False
LOG_LEVEL=INFO
DATABASE_URL=*********************************************/test_materialsearch
CACHE_SIZE=32
SCAN_PROCESS_BATCH_SIZE=4
```

### 生产环境配置
```bash
DEBUG=False
LOG_LEVEL=WARNING
LOG_FORMAT=json
DATABASE_URL=*********************************************/materialsearch
CACHE_SIZE=128
SCAN_PROCESS_BATCH_SIZE=8
ENABLE_CONSOLE_LOG=False
ENABLE_FILE_LOG=True
```

### 高性能配置
```bash
MODEL_BACKEND=onnx
MODEL_PRECISION=fp16
SCAN_PROCESS_BATCH_SIZE=16
CACHE_SIZE=256
DB_POOL_SIZE=50
BACKGROUND_TASK_WORKERS=8
```

### 资源受限配置
```bash
MODEL_NAME=OFA-Sys/chinese-clip-vit-base-patch16
SCAN_PROCESS_BATCH_SIZE=2
CACHE_SIZE=32
DB_POOL_SIZE=10
BACKGROUND_TASK_WORKERS=2
LOG_LEVEL=ERROR
```

## 配置监控和维护

### 配置监控
- **配置变更记录**：记录配置的变更历史
- **配置一致性检查**：检查多实例间配置的一致性
- **配置有效性监控**：监控配置参数的有效性
- **性能影响分析**：分析配置变更对性能的影响

### 配置维护
- **定期审查**：定期审查配置的合理性
- **性能调优**：根据监控数据调整配置参数
- **安全更新**：及时更新安全相关的配置
- **文档更新**：保持配置文档的及时更新

### 故障排除
- **配置诊断**：提供配置诊断工具
- **配置备份**：定期备份重要配置
- **配置恢复**：配置损坏时的快速恢复
- **配置验证**：提供配置验证工具
