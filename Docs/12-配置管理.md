# 配置管理

## 概述

系统采用配置文件 + API接口的双重管理方式，支持多环境配置（开发、测试、预发布、生产）。通过YAML配置文件进行基础配置管理，同时提供REST API接口实现配置的动态查询、更新和热重载，涵盖服务器、数据库、模型、搜索、任务处理、日志等各个方面的配置参数。

## 多环境配置架构

### 配置文件结构
```
config/
├── base.yaml              # 基础配置（所有环境共享）
├── development.yaml       # 开发环境配置
├── testing.yaml          # 测试环境配置
├── staging.yaml           # 预发布环境配置
├── production.yaml        # 生产环境配置
└── local.yaml            # 本地开发配置（可选，不提交到版本控制）
```

### 环境识别
- **环境变量**: `ENVIRONMENT=development|testing|staging|production`
- **默认环境**: development
- **配置加载顺序**: base.yaml → {environment}.yaml → local.yaml

### 配置继承机制
- **基础配置**: base.yaml包含所有环境的通用配置
- **环境特定**: 各环境配置文件覆盖基础配置的特定项
- **本地覆盖**: local.yaml用于本地开发时的个性化配置

## 配置文件详解

### base.yaml - 基础配置
```yaml
# 应用基础配置
app:
  name: "MaterialSearch"
  version: "2.0.0"
  description: "AI-powered local media search system"

# 服务器配置
server:
  host: "127.0.0.1"
  port: 8085
  debug: false
  workers: 1

# 数据库配置
database:
  host: "localhost"
  port: 5432
  name: "materialsearch"
  user: "materialsearch"
  password: ""
  pool_size: 20
  vector_dimension: 512
  timeout: 30

# 模型配置
model:
  name: "OFA-Sys/chinese-clip-vit-base-patch16"
  backend: "transformers"  # transformers/onnx/openvino
  precision: "fp32"        # fp32/fp16/int8
  device: "auto"           # auto/cpu/cuda/mps
  cache_dir: "./models"
  batch_size: 4

# 扫描配置
scan:
  assets_paths:
    - "/data/images"
    - "/data/videos"
  skip_paths:
    - "/tmp"
    - "/var/cache"
  image_extensions:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".gif"
    - ".heic"
    - ".webp"
    - ".bmp"
  video_extensions:
    - ".mp4"
    - ".flv"
    - ".mov"
    - ".mkv"
    - ".webm"
    - ".avi"
  frame_interval: 2
  image_min_width: 64
  image_min_height: 64
  auto_save_interval: 100

# 搜索配置
search:
  cache_size: 64
  positive_threshold: 36
  negative_threshold: 36
  image_threshold: 85

# 任务处理配置
task:
  sync_process_timeout: 30
  task_lock_timeout: 30
  max_retry_attempts: 3
  cleanup_interval: 24
  background_workers: 4
  realtime_enabled: true
  max_concurrent_uploads: 10
  upload_timeout: 300
  auto_async_threshold: 10485760  # 10MB

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file_path: "./logs/app.log"
  max_size: 100
  backup_count: 5
  rotation: "size"
  console_enabled: true
  file_enabled: true

# 认证配置
auth:
  enabled: false
  secret_key: "your-secret-key-here"
  token_expire_minutes: 30
```

### development.yaml - 开发环境配置
```yaml
# 开发环境特定配置
server:
  debug: true
  host: "127.0.0.1"
  port: 8000

database:
  name: "materialsearch_dev"
  user: "dev_user"
  password: "dev_password"
  pool_size: 10

model:
  batch_size: 2  # 开发环境使用较小批量
  cache_dir: "./dev_models"

scan:
  assets_paths:
    - "./test_data/images"
    - "./test_data/videos"
  auto_save_interval: 10  # 更频繁的保存

logging:
  level: "DEBUG"
  console_enabled: true
  file_enabled: false  # 开发环境不写文件日志

task:
  background_workers: 2
  max_concurrent_uploads: 5
```

### testing.yaml - 测试环境配置
```yaml
# 测试环境特定配置
server:
  host: "0.0.0.0"
  port: 8001

database:
  name: "materialsearch_test"
  user: "test_user"
  password: "test_password"
  pool_size: 15

model:
  batch_size: 4
  cache_dir: "./test_models"

scan:
  assets_paths:
    - "/test/images"
    - "/test/videos"

logging:
  level: "INFO"
  file_path: "./logs/test.log"

task:
  background_workers: 4
  cleanup_interval: 1  # 测试环境更频繁清理
```

### staging.yaml - 预发布环境配置
```yaml
# 预发布环境配置
server:
  host: "0.0.0.0"
  port: 8002
  workers: 2

database:
  name: "materialsearch_staging"
  user: "staging_user"
  password: "${STAGING_DB_PASSWORD}"  # 从环境变量读取
  pool_size: 25

model:
  backend: "onnx"  # 预发布使用优化后端
  batch_size: 8
  cache_dir: "/opt/models"

scan:
  assets_paths:
    - "/staging/images"
    - "/staging/videos"

logging:
  level: "WARNING"
  file_path: "/var/log/materialsearch/staging.log"
  format: "json"

task:
  background_workers: 6
  max_concurrent_uploads: 20

auth:
  enabled: true
  secret_key: "${STAGING_SECRET_KEY}"
```

### production.yaml - 生产环境配置
```yaml
# 生产环境配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4

database:
  host: "${PROD_DB_HOST}"
  name: "materialsearch"
  user: "${PROD_DB_USER}"
  password: "${PROD_DB_PASSWORD}"
  pool_size: 50

model:
  backend: "onnx"
  precision: "fp16"
  batch_size: 16
  cache_dir: "/opt/models"

scan:
  assets_paths:
    - "/data/images"
    - "/data/videos"
  skip_paths:
    - "/tmp"
    - "/var/cache"
    - "/data/backup"

search:
  cache_size: 256  # 生产环境更大缓存

logging:
  level: "ERROR"
  file_path: "/var/log/materialsearch/production.log"
  format: "json"
  console_enabled: false
  max_size: 500
  backup_count: 10

task:
  background_workers: 8
  max_concurrent_uploads: 50
  cleanup_interval: 168  # 一周清理一次

auth:
  enabled: true
  secret_key: "${PROD_SECRET_KEY}"
  token_expire_minutes: 60
```

## 配置加载机制

### 配置加载器设计
```python
# config/loader.py
import os
import yaml
from typing import Dict, Any
from pathlib import Path

class ConfigLoader:
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.environment = os.getenv("ENVIRONMENT", "development")

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        # 1. 加载基础配置
        base_config = self._load_yaml("base.yaml")

        # 2. 加载环境特定配置
        env_config = self._load_yaml(f"{self.environment}.yaml")

        # 3. 加载本地配置（如果存在）
        local_config = self._load_yaml("local.yaml", required=False)

        # 4. 合并配置
        config = self._merge_configs(base_config, env_config, local_config)

        # 5. 环境变量替换
        config = self._substitute_env_vars(config)

        return config

    def _load_yaml(self, filename: str, required: bool = True) -> Dict[str, Any]:
        """加载YAML文件"""
        file_path = self.config_dir / filename
        if not file_path.exists():
            if required:
                raise FileNotFoundError(f"配置文件不存在: {file_path}")
            return {}

        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}

    def _merge_configs(self, *configs) -> Dict[str, Any]:
        """深度合并配置"""
        result = {}
        for config in configs:
            if config:
                result = self._deep_merge(result, config)
        return result

    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """替换环境变量"""
        # 递归替换 ${VAR_NAME} 格式的环境变量
        pass
```

### 配置验证器
```python
# config/validator.py
from pydantic import BaseModel, validator
from typing import List, Optional

class ServerConfig(BaseModel):
    host: str = "127.0.0.1"
    port: int = 8085
    debug: bool = False
    workers: int = 1

    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在1-65535之间')
        return v

class DatabaseConfig(BaseModel):
    host: str = "localhost"
    port: int = 5432
    name: str
    user: str
    password: str
    pool_size: int = 20
    vector_dimension: int = 512
    timeout: int = 30

class ModelConfig(BaseModel):
    name: str
    backend: str = "transformers"
    precision: str = "fp32"
    device: str = "auto"
    cache_dir: str = "./models"
    batch_size: int = 4

    @validator('backend')
    def validate_backend(cls, v):
        allowed = ['transformers', 'onnx', 'openvino']
        if v not in allowed:
            raise ValueError(f'backend必须是{allowed}中的一个')
        return v

class AppConfig(BaseModel):
    server: ServerConfig
    database: DatabaseConfig
    model: ModelConfig
    # ... 其他配置
```

## 配置管理最佳实践

### 环境分离策略
- **开发环境**：本地开发，调试模式，小批量处理
- **测试环境**：自动化测试，中等配置，完整功能测试
- **预发布环境**：生产环境镜像，性能测试，最终验证
- **生产环境**：高性能配置，安全加固，监控告警

### 敏感信息管理
- **环境变量**：敏感信息通过环境变量注入
- **密钥管理**：使用专门的密钥管理服务（如HashiCorp Vault）
- **配置加密**：敏感配置文件加密存储
- **访问控制**：严格控制配置文件的访问权限

### 配置验证机制
- **启动验证**：应用启动时验证所有配置参数
- **类型检查**：使用Pydantic进行类型和格式验证
- **依赖检查**：验证外部依赖的可用性
- **兼容性检查**：检查配置参数间的兼容性

### 配置热更新支持
- **文件监控**：监控配置文件变更
- **安全重载**：验证新配置的有效性后再应用
- **渐进式更新**：支持部分配置的热更新
- **回滚机制**：配置更新失败时自动回滚

## 部署和使用

### 环境变量设置
```bash
# 设置运行环境
export ENVIRONMENT=production

# 设置敏感信息
export PROD_DB_HOST=db.example.com
export PROD_DB_USER=materialsearch
export PROD_DB_PASSWORD=secure_password
export PROD_SECRET_KEY=your-super-secret-key
```

### Docker部署配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  materialsearch:
    image: materialsearch:latest
    environment:
      - ENVIRONMENT=production
      - PROD_DB_HOST=postgres
      - PROD_DB_USER=materialsearch
      - PROD_DB_PASSWORD=secure_password
      - PROD_SECRET_KEY=your-super-secret-key
    volumes:
      - ./config:/app/config:ro
      - ./data:/data:ro
      - ./logs:/var/log/materialsearch
    depends_on:
      - postgres

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=materialsearch
      - POSTGRES_USER=materialsearch
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

### Kubernetes配置
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: materialsearch-config
data:
  production.yaml: |
    server:
      host: "0.0.0.0"
      port: 8000
    database:
      host: "postgres-service"
      name: "materialsearch"
    # ... 其他配置

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: materialsearch-secrets
type: Opaque
data:
  db-password: <base64-encoded-password>
  secret-key: <base64-encoded-secret>
```

### 应用启动
```python
# main.py
import os
from config.loader import ConfigLoader
from config.validator import AppConfig

def main():
    # 加载配置
    loader = ConfigLoader()
    raw_config = loader.load_config()

    # 验证配置
    config = AppConfig(**raw_config)

    # 启动应用
    app = create_app(config)
    app.run(host=config.server.host, port=config.server.port)

if __name__ == "__main__":
    main()
```

## 配置监控和维护

### 配置变更管理
- **版本控制**：所有配置文件纳入Git版本控制
- **变更审批**：生产环境配置变更需要审批流程
- **变更记录**：记录配置变更的时间、人员、原因
- **回滚计划**：每次配置变更都要有回滚计划

### 配置监控
- **配置一致性**：监控多实例间配置的一致性
- **配置有效性**：定期验证配置参数的有效性
- **性能影响**：监控配置变更对系统性能的影响
- **安全合规**：检查配置是否符合安全规范

### 配置工具
```python
# 配置验证工具
python -m config.validator --env production

# 配置对比工具
python -m config.diff --env1 staging --env2 production

# 配置导出工具
python -m config.export --env production --format json

# 配置热重载
python -m config.reload --component logging
```

### 故障排除
- **配置诊断**：提供详细的配置诊断信息
- **配置备份**：自动备份配置文件和环境变量
- **快速恢复**：配置问题时的快速恢复机制
- **配置测试**：提供配置有效性测试工具

## 安全考虑

### 敏感信息保护
- **环境变量注入**：敏感信息通过环境变量注入，不存储在配置文件中
- **配置文件权限**：严格控制配置文件的读写权限
- **传输加密**：配置文件传输时使用加密通道
- **审计日志**：记录配置访问和修改的审计日志

### 配置安全最佳实践
- **最小权限原则**：只给必要的权限访问配置
- **定期轮换**：定期轮换密钥和密码
- **安全扫描**：定期扫描配置中的安全问题
- **合规检查**：确保配置符合安全合规要求

## 总结

多环境配置管理系统的优势：

1. **环境隔离**：不同环境使用独立配置，避免相互影响
2. **配置继承**：基础配置共享，环境特定配置覆盖
3. **安全管理**：敏感信息通过环境变量管理，不暴露在代码中
4. **版本控制**：配置文件纳入版本控制，变更可追溯
5. **验证机制**：启动时验证配置有效性，减少运行时错误
6. **热更新支持**：支持部分配置的热更新，提高运维效率

这种配置管理方式更适合企业级应用的部署和运维需求。
