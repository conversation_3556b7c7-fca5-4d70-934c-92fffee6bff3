# 配置管理

## 概述

系统采用配置文件 + API接口的双重管理方式，支持多环境配置（开发、测试、预发布、生产）。通过YAML配置文件进行基础配置管理，同时提供REST API接口实现配置的动态查询、更新和热重载，涵盖服务器、数据库、模型、搜索、任务处理、日志等各个方面的配置参数。

## 多环境配置架构

### 配置文件结构
```
config/
├── base.yaml              # 基础配置（所有环境共享）
├── development.yaml       # 开发环境配置
├── testing.yaml          # 测试环境配置
├── staging.yaml           # 预发布环境配置
├── production.yaml        # 生产环境配置
└── local.yaml            # 本地开发配置（可选，不提交到版本控制）
```

### 环境识别
- **环境变量**: `ENVIRONMENT=development|testing|staging|production`
- **默认环境**: development
- **配置加载顺序**: base.yaml → {environment}.yaml → local.yaml → API动态配置

### 配置管理模式
- **静态配置**: 通过YAML文件管理的基础配置，需要重启生效
- **动态配置**: 通过API接口管理的运行时配置，支持热更新
- **配置优先级**: API动态配置 > 环境配置文件 > 基础配置文件

### 配置继承机制
- **基础配置**: base.yaml包含所有环境的通用配置
- **环境特定**: 各环境配置文件覆盖基础配置的特定项
- **本地覆盖**: local.yaml用于本地开发时的个性化配置

## 配置文件详解

### base.yaml - 基础配置
```yaml
# 应用基础配置
app:
  name: "MaterialSearch"
  version: "2.0.0"
  description: "AI-powered local media search system"

# 服务器配置
server:
  host: "127.0.0.1"
  port: 8085
  debug: false
  workers: 1

# 数据库配置
database:
  host: "localhost"
  port: 5432
  name: "materialsearch"
  user: "materialsearch"
  password: ""
  pool_size: 20
  vector_dimension: 512
  timeout: 30

# 模型配置
model:
  name: "OFA-Sys/chinese-clip-vit-base-patch16"
  backend: "transformers"  # transformers/onnx/openvino
  precision: "fp32"        # fp32/fp16/int8
  device: "auto"           # auto/cpu/cuda/mps
  cache_dir: "./models"
  batch_size: 4

# 扫描配置
scan:
  assets_paths:
    - "/data/images"
    - "/data/videos"
  skip_paths:
    - "/tmp"
    - "/var/cache"
  image_extensions:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".gif"
    - ".heic"
    - ".webp"
    - ".bmp"
  video_extensions:
    - ".mp4"
    - ".flv"
    - ".mov"
    - ".mkv"
    - ".webm"
    - ".avi"
  frame_interval: 2
  image_min_width: 64
  image_min_height: 64
  auto_save_interval: 100

# 搜索配置
search:
  cache_size: 64
  positive_threshold: 36
  negative_threshold: 36
  image_threshold: 85

# 任务处理配置
task:
  sync_process_timeout: 30
  task_lock_timeout: 30
  max_retry_attempts: 3
  cleanup_interval: 24
  background_workers: 4
  realtime_enabled: true
  max_concurrent_uploads: 10
  upload_timeout: 300
  auto_async_threshold: 10485760  # 10MB

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file_path: "./logs/app.log"
  max_size: 100
  backup_count: 5
  rotation: "size"
  console_enabled: true
  file_enabled: true


```

### development.yaml - 开发环境配置
```yaml
# 开发环境特定配置
server:
  debug: true
  host: "127.0.0.1"
  port: 8000

database:
  name: "materialsearch_dev"
  user: "dev_user"
  password: "dev_password"
  pool_size: 10

model:
  batch_size: 2  # 开发环境使用较小批量
  cache_dir: "./dev_models"

scan:
  assets_paths:
    - "./test_data/images"
    - "./test_data/videos"
  auto_save_interval: 10  # 更频繁的保存

logging:
  level: "DEBUG"
  console_enabled: true
  file_enabled: false  # 开发环境不写文件日志

task:
  background_workers: 2
  max_concurrent_uploads: 5
```

### testing.yaml - 测试环境配置
```yaml
# 测试环境特定配置
server:
  host: "0.0.0.0"
  port: 8001

database:
  name: "materialsearch_test"
  user: "test_user"
  password: "test_password"
  pool_size: 15

model:
  batch_size: 4
  cache_dir: "./test_models"

scan:
  assets_paths:
    - "/test/images"
    - "/test/videos"

logging:
  level: "INFO"
  file_path: "./logs/test.log"

task:
  background_workers: 4
  cleanup_interval: 1  # 测试环境更频繁清理
```

### staging.yaml - 预发布环境配置
```yaml
# 预发布环境配置
server:
  host: "0.0.0.0"
  port: 8002
  workers: 2

database:
  name: "materialsearch_staging"
  user: "staging_user"
  password: "${STAGING_DB_PASSWORD}"  # 从环境变量读取
  pool_size: 25

model:
  backend: "onnx"  # 预发布使用优化后端
  batch_size: 8
  cache_dir: "/opt/models"

scan:
  assets_paths:
    - "/staging/images"
    - "/staging/videos"

logging:
  level: "WARNING"
  file_path: "/var/log/materialsearch/staging.log"
  format: "json"

task:
  background_workers: 6
  max_concurrent_uploads: 20


```

### production.yaml - 生产环境配置
```yaml
# 生产环境配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4

database:
  host: "${PROD_DB_HOST}"
  name: "materialsearch"
  user: "${PROD_DB_USER}"
  password: "${PROD_DB_PASSWORD}"
  pool_size: 50

model:
  backend: "onnx"
  precision: "fp16"
  batch_size: 16
  cache_dir: "/opt/models"

scan:
  assets_paths:
    - "/data/images"
    - "/data/videos"
  skip_paths:
    - "/tmp"
    - "/var/cache"
    - "/data/backup"

search:
  cache_size: 256  # 生产环境更大缓存

logging:
  level: "ERROR"
  file_path: "/var/log/materialsearch/production.log"
  format: "json"
  console_enabled: false
  max_size: 500
  backup_count: 10

task:
  background_workers: 8
  max_concurrent_uploads: 50
  cleanup_interval: 168  # 一周清理一次


```

## 配置加载机制

### 配置加载器设计
```python
# config/loader.py
import os
import yaml
from typing import Dict, Any
from pathlib import Path

class ConfigLoader:
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.environment = os.getenv("ENVIRONMENT", "development")

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        # 1. 加载基础配置
        base_config = self._load_yaml("base.yaml")

        # 2. 加载环境特定配置
        env_config = self._load_yaml(f"{self.environment}.yaml")

        # 3. 加载本地配置（如果存在）
        local_config = self._load_yaml("local.yaml", required=False)

        # 4. 合并配置
        config = self._merge_configs(base_config, env_config, local_config)

        # 5. 环境变量替换
        config = self._substitute_env_vars(config)

        # 6. 加载动态配置覆盖
        config = self._apply_dynamic_config(config)

        return config

    def _load_yaml(self, filename: str, required: bool = True) -> Dict[str, Any]:
        """加载YAML文件"""
        file_path = self.config_dir / filename
        if not file_path.exists():
            if required:
                raise FileNotFoundError(f"配置文件不存在: {file_path}")
            return {}

        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}

    def _merge_configs(self, *configs) -> Dict[str, Any]:
        """深度合并配置"""
        result = {}
        for config in configs:
            if config:
                result = self._deep_merge(result, config)
        return result

    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """替换环境变量"""
        # 递归替换 ${VAR_NAME} 格式的环境变量
        pass
```

### 配置验证器
```python
# config/validator.py
from pydantic import BaseModel, validator
from typing import List, Optional

class ServerConfig(BaseModel):
    host: str = "127.0.0.1"
    port: int = 8085
    debug: bool = False
    workers: int = 1

    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在1-65535之间')
        return v

class DatabaseConfig(BaseModel):
    host: str = "localhost"
    port: int = 5432
    name: str
    user: str
    password: str
    pool_size: int = 20
    vector_dimension: int = 512
    timeout: int = 30

class ModelConfig(BaseModel):
    name: str
    backend: str = "transformers"
    precision: str = "fp32"
    device: str = "auto"
    cache_dir: str = "./models"
    batch_size: int = 4

    @validator('backend')
    def validate_backend(cls, v):
        allowed = ['transformers', 'onnx', 'openvino']
        if v not in allowed:
            raise ValueError(f'backend必须是{allowed}中的一个')
        return v

class AppConfig(BaseModel):
    server: ServerConfig
    database: DatabaseConfig
    model: ModelConfig
    # ... 其他配置
```

## 配置管理API接口

### API接口设计

#### 配置查询接口
```python
# app/routes/config.py
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any, Optional
from app.utils.response import ApiResponse
from app.config.manager import ConfigManager

router = APIRouter()

@router.get("/config", response_model=ApiResponse[Dict[str, Any]])
async def get_all_config(
    section: Optional[str] = None,
    config_manager: ConfigManager = Depends(get_config_manager)
) -> ApiResponse[Dict[str, Any]]:
    """获取所有配置或指定配置节"""
    try:
        if section:
            config_data = config_manager.get_section(section)
            if not config_data:
                return ApiResponse(
                    ErrorCode=1001,
                    Msg=f"配置节 '{section}' 不存在",
                    IsSuccess=False,
                    Body=None
                )
        else:
            config_data = config_manager.get_all_config()

        return ApiResponse(
            ErrorCode=0,
            Msg="获取配置成功",
            IsSuccess=True,
            Body=config_data
        )
    except Exception as e:
        return ApiResponse(
            ErrorCode=-1,
            Msg=f"获取配置失败: {str(e)}",
            IsSuccess=False,
            Body=None
        )

@router.get("/config/{section}/{key}", response_model=ApiResponse[Any])
async def get_config_value(
    section: str,
    key: str,
    config_manager: ConfigManager = Depends(get_config_manager)
) -> ApiResponse[Any]:
    """获取指定配置项的值"""
    try:
        value = config_manager.get_value(section, key)
        if value is None:
            return ApiResponse(
                ErrorCode=1001,
                Msg=f"配置项 '{section}.{key}' 不存在",
                IsSuccess=False,
                Body=None
            )

        return ApiResponse(
            ErrorCode=0,
            Msg="获取配置项成功",
            IsSuccess=True,
            Body={"section": section, "key": key, "value": value}
        )
    except Exception as e:
        return ApiResponse(
            ErrorCode=-1,
            Msg=f"获取配置项失败: {str(e)}",
            IsSuccess=False,
            Body=None
        )
```

#### 配置更新接口
```python
@router.put("/config/{section}/{key}", response_model=ApiResponse[None])
async def update_config_value(
    section: str,
    key: str,
    request: UpdateConfigRequest,
    config_manager: ConfigManager = Depends(get_config_manager)
) -> ApiResponse[None]:
    """更新指定配置项的值"""
    try:
        # 验证配置项是否允许动态更新
        if not config_manager.is_dynamic_updatable(section, key):
            return ApiResponse(
                ErrorCode=1002,
                Msg=f"配置项 '{section}.{key}' 不支持动态更新",
                IsSuccess=False,
                Body=None
            )

        # 验证配置值的有效性
        if not config_manager.validate_config_value(section, key, request.value):
            return ApiResponse(
                ErrorCode=1003,
                Msg=f"配置值无效",
                IsSuccess=False,
                Body=None
            )

        # 更新配置
        old_value = config_manager.get_value(section, key)
        config_manager.set_value(section, key, request.value)

        # 记录配置变更
        await log_config_change(section, key, old_value, request.value, request.reason)

        return ApiResponse(
            ErrorCode=0,
            Msg="配置更新成功",
            IsSuccess=True,
            Body=None
        )
    except Exception as e:
        return ApiResponse(
            ErrorCode=-1,
            Msg=f"配置更新失败: {str(e)}",
            IsSuccess=False,
            Body=None
        )

@router.put("/config/{section}", response_model=ApiResponse[None])
async def update_config_section(
    section: str,
    request: UpdateConfigSectionRequest,
    config_manager: ConfigManager = Depends(get_config_manager)
) -> ApiResponse[None]:
    """批量更新配置节"""
    try:
        # 验证配置节是否存在
        if not config_manager.has_section(section):
            return ApiResponse(
                ErrorCode=1001,
                Msg=f"配置节 '{section}' 不存在",
                IsSuccess=False,
                Body=None
            )

        # 批量验证和更新
        old_config = config_manager.get_section(section)
        for key, value in request.config.items():
            if not config_manager.is_dynamic_updatable(section, key):
                return ApiResponse(
                    ErrorCode=1002,
                    Msg=f"配置项 '{section}.{key}' 不支持动态更新",
                    IsSuccess=False,
                    Body=None
                )

            if not config_manager.validate_config_value(section, key, value):
                return ApiResponse(
                    ErrorCode=1003,
                    Msg=f"配置项 '{section}.{key}' 的值无效",
                    IsSuccess=False,
                    Body=None
                )

        # 执行批量更新
        config_manager.update_section(section, request.config)

        # 记录配置变更
        await log_config_section_change(section, old_config, request.config, request.reason)

        return ApiResponse(
            ErrorCode=0,
            Msg="配置节更新成功",
            IsSuccess=True,
            Body=None
        )
    except Exception as e:
        return ApiResponse(
            ErrorCode=-1,
            Msg=f"配置节更新失败: {str(e)}",
            IsSuccess=False,
            Body=None
        )
```

#### 配置重载接口
```python
@router.post("/config/reload", response_model=ApiResponse[None])
async def reload_config(
    request: ReloadConfigRequest,
    config_manager: ConfigManager = Depends(get_config_manager)
) -> ApiResponse[None]:
    """重新加载配置文件"""
    try:
        if request.section:
            # 重载指定配置节
            config_manager.reload_section(request.section)
            message = f"配置节 '{request.section}' 重载成功"
        else:
            # 重载所有配置
            config_manager.reload_all()
            message = "所有配置重载成功"

        # 记录重载操作
        await log_config_reload(request.section, request.reason)

        return ApiResponse(
            ErrorCode=0,
            Msg=message,
            IsSuccess=True,
            Body=None
        )
    except Exception as e:
        return ApiResponse(
            ErrorCode=-1,
            Msg=f"配置重载失败: {str(e)}",
            IsSuccess=False,
            Body=None
        )

@router.post("/config/reset", response_model=ApiResponse[None])
async def reset_config(
    request: ResetConfigRequest,
    config_manager: ConfigManager = Depends(get_config_manager)
) -> ApiResponse[None]:
    """重置配置到默认值"""
    try:
        if request.section and request.key:
            # 重置指定配置项
            config_manager.reset_value(request.section, request.key)
            message = f"配置项 '{request.section}.{request.key}' 重置成功"
        elif request.section:
            # 重置指定配置节
            config_manager.reset_section(request.section)
            message = f"配置节 '{request.section}' 重置成功"
        else:
            # 重置所有动态配置
            config_manager.reset_all_dynamic()
            message = "所有动态配置重置成功"

        # 记录重置操作
        await log_config_reset(request.section, request.key, request.reason)

        return ApiResponse(
            ErrorCode=0,
            Msg=message,
            IsSuccess=True,
            Body=None
        )
    except Exception as e:
        return ApiResponse(
            ErrorCode=-1,
            Msg=f"配置重置失败: {str(e)}",
            IsSuccess=False,
            Body=None
        )
```

## 简化的配置管理

配置管理保持简洁，专注于核心功能：
- 配置查询和更新
- 基本的配置重载
- 简单的变更记录

### 请求响应模型

#### 请求模型
```python
# app/models/config.py
from pydantic import BaseModel
from typing import Dict, Any, Optional
from datetime import datetime

class UpdateConfigRequest(BaseModel):
    value: Any
    reason: Optional[str] = None

class UpdateConfigSectionRequest(BaseModel):
    config: Dict[str, Any]
    reason: Optional[str] = None

class ReloadConfigRequest(BaseModel):
    section: Optional[str] = None
    reason: Optional[str] = None

class ResetConfigRequest(BaseModel):
    section: Optional[str] = None
    key: Optional[str] = None
    reason: Optional[str] = None

class ConfigChangeRecord(BaseModel):
    id: int
    section: str
    key: str
    old_value: Any
    new_value: Any
    change_type: str  # UPDATE/RESET/RELOAD
    reason: Optional[str]
    changed_by: Optional[str]
    changed_at: datetime
    environment: str
```

#### 响应模型
```python
class ConfigValueResponse(BaseModel):
    section: str
    key: str
    value: Any
    is_dynamic: bool
    last_updated: Optional[datetime]

class ConfigSectionResponse(BaseModel):
    section: str
    config: Dict[str, Any]
    dynamic_keys: List[str]
    last_updated: Optional[datetime]

class ConfigSchemaItem(BaseModel):
    key: str
    type: str
    default_value: Any
    description: str
    is_dynamic: bool
    validation_rules: Optional[Dict[str, Any]]

class ConfigSectionSchema(BaseModel):
    section: str
    description: str
    items: List[ConfigSchemaItem]
```

## 动态配置管理器

### ConfigManager扩展
```python
# app/config/manager.py
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import json
from app.models.config import ConfigChangeRecord

class ConfigManager:
    def __init__(self):
        self.static_config = {}
        self.dynamic_config = {}
        self.config_schema = self._load_config_schema()
        self.dynamic_updatable_keys = self._load_dynamic_keys()

    def get_value(self, section: str, key: str) -> Any:
        """获取配置值（动态配置优先）"""
        # 1. 检查动态配置
        dynamic_key = f"{section}.{key}"
        if dynamic_key in self.dynamic_config:
            return self.dynamic_config[dynamic_key]

        # 2. 检查静态配置
        if section in self.static_config and key in self.static_config[section]:
            return self.static_config[section][key]

        return None

    def set_value(self, section: str, key: str, value: Any):
        """设置动态配置值"""
        dynamic_key = f"{section}.{key}"
        self.dynamic_config[dynamic_key] = value

        # 持久化到数据库
        self._save_dynamic_config(dynamic_key, value)

        # 触发配置变更事件
        asyncio.create_task(self._notify_config_change(section, key, value))

    def is_dynamic_updatable(self, section: str, key: str) -> bool:
        """检查配置项是否支持动态更新"""
        return f"{section}.{key}" in self.dynamic_updatable_keys

    def validate_config_value(self, section: str, key: str, value: Any) -> bool:
        """验证配置值的有效性"""
        schema_key = f"{section}.{key}"
        if schema_key not in self.config_schema:
            return False

        schema = self.config_schema[schema_key]

        # 类型检查
        expected_type = schema.get("type")
        if expected_type and not isinstance(value, eval(expected_type)):
            return False

        # 范围检查
        if "min_value" in schema and value < schema["min_value"]:
            return False
        if "max_value" in schema and value > schema["max_value"]:
            return False

        # 枚举检查
        if "allowed_values" in schema and value not in schema["allowed_values"]:
            return False

        return True

    async def get_config_history(
        self,
        section: Optional[str] = None,
        key: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[ConfigChangeRecord]:
        """获取配置变更历史"""
        # 从数据库查询配置变更记录
        query = "SELECT * FROM config_changes WHERE 1=1"
        params = []

        if section:
            query += " AND section = ?"
            params.append(section)

        if key:
            query += " AND key = ?"
            params.append(key)

        query += " ORDER BY changed_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        # 执行查询并返回结果
        # 这里需要根据实际的数据库实现
        pass

    def reload_section(self, section: str):
        """重新加载指定配置节"""
        # 重新加载配置文件中的指定节
        pass

    def reload_all(self):
        """重新加载所有配置"""
        # 重新加载所有配置文件
        pass

    def reset_value(self, section: str, key: str):
        """重置配置项到默认值"""
        dynamic_key = f"{section}.{key}"
        if dynamic_key in self.dynamic_config:
            del self.dynamic_config[dynamic_key]
            self._remove_dynamic_config(dynamic_key)

    def reset_section(self, section: str):
        """重置配置节到默认值"""
        keys_to_remove = [k for k in self.dynamic_config.keys() if k.startswith(f"{section}.")]
        for key in keys_to_remove:
            del self.dynamic_config[key]
            self._remove_dynamic_config(key)

    def reset_all_dynamic(self):
        """重置所有动态配置"""
        self.dynamic_config.clear()
        self._clear_all_dynamic_config()

    async def _notify_config_change(self, section: str, key: str, value: Any):
        """通知配置变更"""
        # 发送WebSocket通知
        # 触发相关组件的配置更新
        pass
```

## 前端配置管理界面

### 配置管理页面设计
```html
<!-- templates/config.html -->
<div x-data="configManager()" class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">系统配置</h1>
            <p class="text-sm text-gray-600">管理系统运行时配置参数</p>
        </div>
        <div class="flex space-x-3">
            <button @click="reloadConfig()"
                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                重载配置
            </button>
            <button @click="exportConfig()"
                    class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                导出配置
            </button>
        </div>
    </div>

    <!-- 配置节选择 -->
    <div class="bg-white rounded-lg shadow">
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <template x-for="section in configSections" :key="section.name">
                    <button @click="activeSection = section.name"
                            :class="activeSection === section.name ?
                                'border-blue-500 text-blue-600' :
                                'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            x-text="section.title">
                    </button>
                </template>
            </nav>
        </div>

        <!-- 配置项列表 -->
        <div class="p-6">
            <template x-for="item in getCurrentSectionConfig()" :key="item.key">
                <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            <h3 class="text-sm font-medium text-gray-900" x-text="item.key"></h3>
                            <span x-show="item.is_dynamic"
                                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                动态
                            </span>
                            <span x-show="!item.is_dynamic"
                                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                静态
                            </span>
                        </div>
                        <div class="flex space-x-2">
                            <button x-show="item.is_dynamic"
                                    @click="editConfig(activeSection, item.key, item.value)"
                                    class="text-blue-600 hover:text-blue-800 text-sm">
                                编辑
                            </button>
                            <button x-show="item.is_dynamic"
                                    @click="resetConfig(activeSection, item.key)"
                                    class="text-red-600 hover:text-red-800 text-sm">
                                重置
                            </button>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 mb-2" x-text="item.description"></p>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-500">当前值:</span>
                        <code class="bg-gray-100 px-2 py-1 rounded text-sm" x-text="formatValue(item.value)"></code>
                        <span x-show="item.last_updated" class="text-xs text-gray-400">
                            最后更新: <span x-text="formatDate(item.last_updated)"></span>
                        </span>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- 配置编辑模态框 -->
    <div x-show="showEditModal"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click.self="closeEditModal()">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">编辑配置项</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">配置项</label>
                        <input type="text" :value="editingConfig.section + '.' + editingConfig.key"
                               readonly class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">配置值</label>
                        <textarea x-model="editingConfig.value"
                                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                 rows="3"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">变更原因</label>
                        <input type="text" x-model="editingConfig.reason"
                               placeholder="请输入变更原因..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button @click="closeEditModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        取消
                    </button>
                    <button @click="saveConfig()"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置变更历史 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">配置变更历史</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">配置项</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更类型</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">旧值</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">新值</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更原因</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="record in configHistory" :key="record.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span x-text="record.section + '.' + record.key"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span :class="getChangeTypeClass(record.change_type)"
                                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                      x-text="record.change_type">
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <code class="bg-gray-100 px-1 py-0.5 rounded text-xs" x-text="formatValue(record.old_value)"></code>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <code class="bg-gray-100 px-1 py-0.5 rounded text-xs" x-text="formatValue(record.new_value)"></code>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(record.changed_at)"></td>
                            <td class="px-6 py-4 text-sm text-gray-500" x-text="record.reason || '-'"></td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>
```

### JavaScript配置管理逻辑
```javascript
// static/js/config-manager.js
function configManager() {
    return {
        activeSection: 'server',
        configSections: [
            { name: 'server', title: '服务器配置' },
            { name: 'database', title: '数据库配置' },
            { name: 'model', title: '模型配置' },
            { name: 'search', title: '搜索配置' },
            { name: 'task', title: '任务配置' },
            { name: 'logging', title: '日志配置' }
        ],
        configData: {},
        configHistory: [],
        showEditModal: false,
        editingConfig: {
            section: '',
            key: '',
            value: '',
            reason: ''
        },

        async init() {
            await this.loadAllConfig();
            await this.loadConfigHistory();
            this.setupWebSocket();
        },

        async loadAllConfig() {
            try {
                const response = await axios.get('/api/config');
                if (response.data.IsSuccess) {
                    this.configData = response.data.Body;
                }
            } catch (error) {
                this.showNotification('加载配置失败', 'error');
            }
        },

        async loadConfigHistory() {
            try {
                const response = await axios.get('/api/config/history?limit=20');
                if (response.data.IsSuccess) {
                    this.configHistory = response.data.Body;
                }
            } catch (error) {
                this.showNotification('加载配置历史失败', 'error');
            }
        },

        getCurrentSectionConfig() {
            const section = this.configData[this.activeSection];
            if (!section) return [];

            return Object.keys(section).map(key => ({
                key,
                value: section[key],
                is_dynamic: this.isDynamicKey(this.activeSection, key),
                description: this.getKeyDescription(this.activeSection, key),
                last_updated: this.getLastUpdated(this.activeSection, key)
            }));
        },

        editConfig(section, key, value) {
            this.editingConfig = {
                section,
                key,
                value: typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value),
                reason: ''
            };
            this.showEditModal = true;
        },

        async saveConfig() {
            try {
                let value = this.editingConfig.value;

                // 尝试解析JSON
                try {
                    value = JSON.parse(value);
                } catch (e) {
                    // 如果不是JSON，保持原值
                }

                const response = await axios.put(
                    `/api/config/${this.editingConfig.section}/${this.editingConfig.key}`,
                    {
                        value: value,
                        reason: this.editingConfig.reason
                    }
                );

                if (response.data.IsSuccess) {
                    this.showNotification('配置更新成功', 'success');
                    await this.loadAllConfig();
                    await this.loadConfigHistory();
                    this.closeEditModal();
                } else {
                    this.showNotification(response.data.Msg, 'error');
                }
            } catch (error) {
                this.showNotification('配置更新失败', 'error');
            }
        },

        async resetConfig(section, key) {
            if (!confirm(`确定要重置配置项 ${section}.${key} 吗？`)) return;

            try {
                const response = await axios.post('/api/config/reset', {
                    section,
                    key,
                    reason: '手动重置'
                });

                if (response.data.IsSuccess) {
                    this.showNotification('配置重置成功', 'success');
                    await this.loadAllConfig();
                    await this.loadConfigHistory();
                } else {
                    this.showNotification(response.data.Msg, 'error');
                }
            } catch (error) {
                this.showNotification('配置重置失败', 'error');
            }
        },

        async reloadConfig() {
            if (!confirm('确定要重新加载所有配置吗？')) return;

            try {
                const response = await axios.post('/api/config/reload', {
                    reason: '手动重载'
                });

                if (response.data.IsSuccess) {
                    this.showNotification('配置重载成功', 'success');
                    await this.loadAllConfig();
                } else {
                    this.showNotification(response.data.Msg, 'error');
                }
            } catch (error) {
                this.showNotification('配置重载失败', 'error');
            }
        },

        closeEditModal() {
            this.showEditModal = false;
            this.editingConfig = { section: '', key: '', value: '', reason: '' };
        },

        formatValue(value) {
            if (typeof value === 'object') {
                return JSON.stringify(value);
            }
            return String(value);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN');
        },

        getChangeTypeClass(type) {
            const classes = {
                'UPDATE': 'bg-blue-100 text-blue-800',
                'RESET': 'bg-yellow-100 text-yellow-800',
                'RELOAD': 'bg-green-100 text-green-800'
            };
            return classes[type] || 'bg-gray-100 text-gray-800';
        },

        setupWebSocket() {
            const ws = new WebSocket(`ws://${window.location.host}/ws/config`);
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'config_changed') {
                    this.loadAllConfig();
                    this.loadConfigHistory();
                    this.showNotification(`配置 ${data.section}.${data.key} 已更新`, 'info');
                }
            };
        }
    }
}
```

## 配置管理最佳实践

### 环境分离策略
- **开发环境**：本地开发，调试模式，小批量处理
- **测试环境**：自动化测试，中等配置，完整功能测试
- **预发布环境**：生产环境镜像，性能测试，最终验证
- **生产环境**：高性能配置，安全加固，监控告警

### 敏感信息管理
- **环境变量**：敏感信息通过环境变量注入
- **密钥管理**：使用专门的密钥管理服务（如HashiCorp Vault）
- **配置加密**：敏感配置文件加密存储
- **访问控制**：严格控制配置文件的访问权限

### 配置验证机制
- **启动验证**：应用启动时验证所有配置参数
- **类型检查**：使用Pydantic进行类型和格式验证
- **依赖检查**：验证外部依赖的可用性
- **兼容性检查**：检查配置参数间的兼容性

### 配置热更新支持
- **文件监控**：监控配置文件变更
- **安全重载**：验证新配置的有效性后再应用
- **渐进式更新**：支持部分配置的热更新
- **回滚机制**：配置更新失败时自动回滚

## 部署和使用

### 环境变量设置
```bash
# 设置运行环境
export ENVIRONMENT=production

# 设置敏感信息
export PROD_DB_HOST=db.example.com
export PROD_DB_USER=materialsearch
export PROD_DB_PASSWORD=secure_password
export PROD_SECRET_KEY=your-super-secret-key
```

### Docker部署配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  materialsearch:
    image: materialsearch:latest
    environment:
      - ENVIRONMENT=production
      - PROD_DB_HOST=postgres
      - PROD_DB_USER=materialsearch
      - PROD_DB_PASSWORD=secure_password
      - PROD_SECRET_KEY=your-super-secret-key
    volumes:
      - ./config:/app/config:ro
      - ./data:/data:ro
      - ./logs:/var/log/materialsearch
    depends_on:
      - postgres

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=materialsearch
      - POSTGRES_USER=materialsearch
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

### Kubernetes配置
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: materialsearch-config
data:
  production.yaml: |
    server:
      host: "0.0.0.0"
      port: 8000
    database:
      host: "postgres-service"
      name: "materialsearch"
    # ... 其他配置

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: materialsearch-secrets
type: Opaque
data:
  db-password: <base64-encoded-password>
  secret-key: <base64-encoded-secret>
```

### 应用启动
```python
# main.py
import os
from config.loader import ConfigLoader
from config.validator import AppConfig

def main():
    # 加载配置
    loader = ConfigLoader()
    raw_config = loader.load_config()

    # 验证配置
    config = AppConfig(**raw_config)

    # 启动应用
    app = create_app(config)
    app.run(host=config.server.host, port=config.server.port)

if __name__ == "__main__":
    main()
```

## 配置监控和维护

### 配置变更管理
- **版本控制**：所有配置文件纳入Git版本控制
- **变更审批**：生产环境配置变更需要审批流程
- **变更记录**：记录配置变更的时间、人员、原因
- **回滚计划**：每次配置变更都要有回滚计划

### 配置监控
- **配置一致性**：监控多实例间配置的一致性
- **配置有效性**：定期验证配置参数的有效性
- **性能影响**：监控配置变更对系统性能的影响
- **安全合规**：检查配置是否符合安全规范

### 配置工具
```python
# 配置验证工具
python -m config.validator --env production

# 配置对比工具
python -m config.diff --env1 staging --env2 production

# 配置导出工具
python -m config.export --env production --format json

# 配置热重载
python -m config.reload --component logging
```

### 故障排除
- **配置诊断**：提供详细的配置诊断信息
- **配置备份**：自动备份配置文件和环境变量
- **快速恢复**：配置问题时的快速恢复机制
- **配置测试**：提供配置有效性测试工具

## 安全考虑

### 敏感信息保护
- **环境变量注入**：敏感信息通过环境变量注入，不存储在配置文件中
- **配置文件权限**：严格控制配置文件的读写权限
- **传输加密**：配置文件传输时使用加密通道
- **审计日志**：记录配置访问和修改的审计日志

### 配置安全最佳实践
- **最小权限原则**：只给必要的权限访问配置
- **定期轮换**：定期轮换密钥和密码
- **安全扫描**：定期扫描配置中的安全问题
- **合规检查**：确保配置符合安全合规要求

## 总结

多环境配置管理系统的优势：

1. **环境隔离**：不同环境使用独立配置，避免相互影响
2. **配置继承**：基础配置共享，环境特定配置覆盖
3. **安全管理**：敏感信息通过环境变量管理，不暴露在代码中
4. **版本控制**：配置文件纳入版本控制，变更可追溯
5. **验证机制**：启动时验证配置有效性，减少运行时错误
6. **热更新支持**：支持部分配置的热更新，提高运维效率

## 配置安全

### 基础安全措施
- **配置验证**：严格的配置值类型和范围验证
- **变更记录**：完整的配置变更历史记录
- **备份机制**：自动配置备份和恢复功能

### 配置审计
```python
# app/services/config_audit.py
import asyncio
from datetime import datetime
from app.models.config import ConfigChangeRecord
from app.utils.logger import get_logger

logger = get_logger(__name__)

async def log_config_change(
    section: str,
    key: str,
    old_value: Any,
    new_value: Any,
    reason: str = None,
    user_id: str = None
):
    """记录配置变更"""
    try:
        record = ConfigChangeRecord(
            section=section,
            key=key,
            old_value=old_value,
            new_value=new_value,
            change_type="UPDATE",
            reason=reason,
            changed_by=user_id,
            changed_at=datetime.now(),
            environment=os.getenv("ENVIRONMENT", "development")
        )

        # 保存到数据库
        await save_config_change_record(record)

        # 记录日志
        logger.info(
            f"配置变更: {section}.{key}",
            extra={
                "section": section,
                "key": key,
                "old_value": old_value,
                "new_value": new_value,
                "user_id": user_id,
                "reason": reason
            }
        )

        # 发送通知
        await notify_config_change(record)

    except Exception as e:
        logger.error(f"记录配置变更失败: {e}")
```

## 配置备份和恢复

### 自动备份
```python
# app/services/config_backup.py
import json
import os
from datetime import datetime
from typing import Dict, Any

class ConfigBackupService:
    def __init__(self, backup_dir: str = "./config_backups"):
        self.backup_dir = backup_dir
        os.makedirs(backup_dir, exist_ok=True)

    async def create_backup(self, config_data: Dict[str, Any]) -> str:
        """创建配置备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"config_backup_{timestamp}.json"
        backup_path = os.path.join(self.backup_dir, backup_filename)

        backup_data = {
            "timestamp": timestamp,
            "environment": os.getenv("ENVIRONMENT"),
            "config": config_data,
            "metadata": {
                "created_by": "system",
                "backup_type": "auto"
            }
        }

        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, ensure_ascii=False)

        return backup_path

    async def restore_backup(self, backup_file: str) -> Dict[str, Any]:
        """恢复配置备份"""
        with open(backup_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)

        return backup_data["config"]

    async def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有备份"""
        backups = []
        for filename in os.listdir(self.backup_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.backup_dir, filename)
                stat = os.stat(filepath)
                backups.append({
                    "filename": filename,
                    "filepath": filepath,
                    "size": stat.st_size,
                    "created_at": datetime.fromtimestamp(stat.st_ctime)
                })

        return sorted(backups, key=lambda x: x["created_at"], reverse=True)
```

## 总结

配置文件 + API接口的双重管理系统具有以下优势：

### 1. 灵活的配置管理
- **静态配置**：通过YAML文件管理基础配置，适合需要重启生效的核心配置
- **动态配置**：通过API接口管理运行时配置，支持热更新
- **多环境支持**：完整的开发、测试、预发布、生产环境配置管理

### 2. 完整的API接口
- **配置查询**：支持全量和单项配置查询
- **配置更新**：支持单项和批量配置更新
- **配置重载**：支持配置文件的热重载
- **配置重置**：支持配置项的重置操作
- **历史查询**：完整的配置变更历史记录

### 3. 现代化的管理界面
- **Web界面**：基于FastAPI的现代化配置管理界面
- **实时更新**：通过WebSocket实现配置变更的实时通知
- **权限控制**：完整的权限管理和操作审计
- **用户友好**：直观的配置编辑和历史查看功能

### 4. 企业级特性
- **安全控制**：敏感配置的权限控制和审计日志
- **备份恢复**：自动配置备份和一键恢复功能
- **变更追踪**：完整的配置变更历史和原因记录
- **多环境隔离**：不同环境的配置完全隔离

### 5. 运维友好
- **热更新**：支持大部分配置的热更新，无需重启服务
- **配置验证**：完整的配置值验证和类型检查
- **错误处理**：友好的错误提示和回滚机制
- **监控集成**：与系统监控和告警系统的集成

这种配置管理方式既保持了配置文件的简单性和可维护性，又提供了API接口的灵活性和动态性，非常适合现代化的企业级应用部署和运维需求。
