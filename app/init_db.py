"""
数据库初始化脚本

用于初始化数据库表结构和基础数据。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config.settings import get_settings
from app.core.database import init_database, create_indexes, check_database_connection
from app.utils.logger import setup_logging, get_logger


async def main():
    """主函数"""
    # 获取配置
    settings = get_settings()
    
    # 设置日志
    setup_logging(
        level="INFO",
        format_type="text",
        console_enabled=True,
        file_enabled=False
    )
    
    logger = get_logger(__name__)
    
    try:
        logger.info("开始初始化数据库...")
        
        # 检查数据库连接
        logger.info("检查数据库连接...")
        if not await check_database_connection(settings):
            logger.error("数据库连接失败，请检查配置")
            sys.exit(1)
        
        # 初始化数据库
        logger.info("初始化数据库表结构...")
        await init_database(settings)
        
        # 创建索引
        logger.info("创建数据库索引...")
        await create_indexes()
        
        logger.info("数据库初始化完成！")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
