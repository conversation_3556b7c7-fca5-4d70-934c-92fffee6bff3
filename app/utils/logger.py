"""
统一日志管理系统

提供结构化日志、日志轮转、多输出等功能。
"""

import os
import sys
import json
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

import structlog
from rich.console import Console
from rich.logging import RichHandler


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_data.update(record.extra_fields)
        
        return json.dumps(log_data, ensure_ascii=False)


class TextFormatter(logging.Formatter):
    """文本格式化器"""
    
    def __init__(self):
        super().__init__(
            fmt="%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )


class Logger:
    """统一日志管理器"""
    
    def __init__(self):
        self.loggers: Dict[str, logging.Logger] = {}
        self.console = Console()
        self._configured = False
    
    def setup_logging(
        self,
        level: str = "INFO",
        format_type: str = "json",
        file_path: Optional[str] = None,
        max_size: int = 100,
        backup_count: int = 5,
        console_enabled: bool = True,
        file_enabled: bool = True
    ):
        """设置日志配置"""
        if self._configured:
            return
        
        # 设置根日志级别
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器
        if console_enabled:
            if format_type == "json":
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setFormatter(JSONFormatter())
            else:
                console_handler = RichHandler(
                    console=self.console,
                    show_time=True,
                    show_path=True,
                    markup=True,
                    rich_tracebacks=True
                )
                console_handler.setFormatter(TextFormatter())
            
            console_handler.setLevel(getattr(logging, level.upper()))
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        if file_enabled and file_path:
            # 确保日志目录存在
            log_file = Path(file_path)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                filename=file_path,
                maxBytes=max_size * 1024 * 1024,  # 转换为字节
                backupCount=backup_count,
                encoding='utf-8'
            )
            
            if format_type == "json":
                file_handler.setFormatter(JSONFormatter())
            else:
                file_handler.setFormatter(TextFormatter())
            
            file_handler.setLevel(getattr(logging, level.upper()))
            root_logger.addHandler(file_handler)
        
        # 配置structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer() if format_type == "json" else structlog.dev.ConsoleRenderer(),
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        self._configured = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def get_structured_logger(self, name: str) -> structlog.BoundLogger:
        """获取结构化日志器"""
        return structlog.get_logger(name)


# 全局日志管理器实例
_logger_manager = Logger()


def setup_logging(
    level: str = "INFO",
    format_type: str = "json",
    file_path: Optional[str] = None,
    max_size: int = 100,
    backup_count: int = 5,
    console_enabled: bool = True,
    file_enabled: bool = True
):
    """设置全局日志配置"""
    _logger_manager.setup_logging(
        level=level,
        format_type=format_type,
        file_path=file_path,
        max_size=max_size,
        backup_count=backup_count,
        console_enabled=console_enabled,
        file_enabled=file_enabled
    )


def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return _logger_manager.get_logger(name)


def get_structured_logger(name: str) -> structlog.BoundLogger:
    """获取结构化日志器"""
    return _logger_manager.get_structured_logger(name)


class LoggerMixin:
    """日志混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)
    
    @property
    def struct_logger(self) -> structlog.BoundLogger:
        """获取当前类的结构化日志器"""
        return get_structured_logger(self.__class__.__module__ + "." + self.__class__.__name__)


# 日志装饰器
def log_function_call(logger_name: Optional[str] = None):
    """记录函数调用的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            logger.info(f"调用函数: {func.__name__}", extra={
                "function": func.__name__,
                "args": str(args)[:200],  # 限制长度
                "kwargs": str(kwargs)[:200]
            })
            
            try:
                result = func(*args, **kwargs)
                logger.info(f"函数执行完成: {func.__name__}")
                return result
            except Exception as e:
                logger.error(f"函数执行失败: {func.__name__}", exc_info=True)
                raise
        
        return wrapper
    return decorator


async def log_async_function_call(logger_name: Optional[str] = None):
    """记录异步函数调用的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            logger.info(f"调用异步函数: {func.__name__}", extra={
                "function": func.__name__,
                "args": str(args)[:200],
                "kwargs": str(kwargs)[:200]
            })
            
            try:
                result = await func(*args, **kwargs)
                logger.info(f"异步函数执行完成: {func.__name__}")
                return result
            except Exception as e:
                logger.error(f"异步函数执行失败: {func.__name__}", exc_info=True)
                raise
        
        return wrapper
    return decorator
