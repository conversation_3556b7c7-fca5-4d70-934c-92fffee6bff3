"""
自定义异常类

定义应用特定的异常类型和错误处理。
"""

from typing import Optional, Any, Dict
from app.utils.response import ErrorCode


class MaterialSearchException(Exception):
    """MaterialSearch基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details
        }


class ValidationException(MaterialSearchException):
    """参数验证异常"""
    
    def __init__(self, message: str, field: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_PARAMETER,
            details={"field": field} if field else {}
        )


class DatabaseException(MaterialSearchException):
    """数据库异常"""
    
    def __init__(self, message: str, operation: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_ERROR,
            details={"operation": operation} if operation else {}
        )


class ModelException(MaterialSearchException):
    """模型相关异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.MODEL_LOAD_FAILED,
            details={"model_name": model_name} if model_name else {}
        )


class FileException(MaterialSearchException):
    """文件处理异常"""
    
    def __init__(self, message: str, file_path: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.FILE_PROCESSING_FAILED,
            details={"file_path": file_path} if file_path else {}
        )


class SearchException(MaterialSearchException):
    """搜索异常"""
    
    def __init__(self, message: str, query: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.SEARCH_FAILED,
            details={"query": query} if query else {}
        )


class TaskException(MaterialSearchException):
    """任务处理异常"""
    
    def __init__(self, message: str, task_id: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.TASK_EXECUTION_FAILED,
            details={"task_id": task_id} if task_id else {}
        )


class ConfigException(MaterialSearchException):
    """配置异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.CONFIG_LOAD_FAILED,
            details={"config_key": config_key} if config_key else {}
        )


class SystemException(MaterialSearchException):
    """系统异常"""
    
    def __init__(self, message: str, component: Optional[str] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.SYSTEM_ERROR,
            details={"component": component} if component else {}
        )


# 异常处理装饰器
def handle_exceptions(default_error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR):
    """异常处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except MaterialSearchException:
                # 重新抛出自定义异常
                raise
            except Exception as e:
                # 将其他异常转换为自定义异常
                raise MaterialSearchException(
                    message=str(e),
                    error_code=default_error_code
                )
        return wrapper
    return decorator


def handle_async_exceptions(default_error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR):
    """异步异常处理装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except MaterialSearchException:
                # 重新抛出自定义异常
                raise
            except Exception as e:
                # 将其他异常转换为自定义异常
                raise MaterialSearchException(
                    message=str(e),
                    error_code=default_error_code
                )
        return wrapper
    return decorator
