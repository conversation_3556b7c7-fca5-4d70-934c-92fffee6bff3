"""
数据库连接和初始化

管理数据库连接、初始化和配置。
"""

import os
from typing import Dict, Any

from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise

from app.config.settings import Settings
from app.utils.logger import get_logger
from app.utils.exceptions import DatabaseException

logger = get_logger(__name__)


def get_database_config(settings: Settings) -> Dict[str, Any]:
    """获取数据库配置"""
    return {
        "connections": {
            "default": {
                "engine": "tortoise.backends.asyncpg",
                "credentials": {
                    "host": settings.database.host,
                    "port": settings.database.port,
                    "user": settings.database.user,
                    "password": settings.database.password,
                    "database": settings.database.name,
                    "minsize": 1,
                    "maxsize": settings.database.pool_size,
                    "command_timeout": settings.database.timeout,
                }
            }
        },
        "apps": {
            "models": {
                "models": ["app.models.database"],
                "default_connection": "default",
            }
        },
        "use_tz": True,
        "timezone": "UTC"
    }


async def init_database(settings: Settings) -> None:
    """初始化数据库连接"""
    try:
        config = get_database_config(settings)
        
        # 初始化Tortoise ORM
        await Tortoise.init(config=config)
        
        # 创建表结构
        await Tortoise.generate_schemas()
        
        # 创建pgvector扩展
        await create_vector_extension()
        
        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise DatabaseException(f"数据库初始化失败: {e}")


async def close_database() -> None:
    """关闭数据库连接"""
    try:
        await Tortoise.close_connections()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接失败: {e}")


async def create_vector_extension() -> None:
    """创建pgvector扩展"""
    try:
        conn = Tortoise.get_connection("default")
        
        # 创建vector扩展
        await conn.execute_query("CREATE EXTENSION IF NOT EXISTS vector;")
        
        # 检查扩展是否创建成功
        result = await conn.execute_query(
            "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector');"
        )
        
        if result[1] and result[1][0][0]:
            logger.info("pgvector扩展已启用")
        else:
            logger.warning("pgvector扩展未启用，向量搜索功能可能不可用")
            
    except Exception as e:
        logger.warning(f"创建pgvector扩展失败: {e}")


def register_database(app, settings: Settings) -> None:
    """注册数据库到FastAPI应用"""
    config = get_database_config(settings)
    
    register_tortoise(
        app,
        config=config,
        generate_schemas=True,
        add_exception_handlers=True,
    )
    
    logger.info("数据库已注册到FastAPI应用")


async def check_database_connection(settings: Settings) -> bool:
    """检查数据库连接"""
    try:
        config = get_database_config(settings)
        
        # 临时连接测试
        await Tortoise.init(config=config)
        
        # 执行简单查询
        conn = Tortoise.get_connection("default")
        await conn.execute_query("SELECT 1;")
        
        await Tortoise.close_connections()
        
        logger.info("数据库连接测试成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False


async def get_database_status() -> Dict[str, Any]:
    """获取数据库状态信息"""
    try:
        conn = Tortoise.get_connection("default")
        
        # 获取数据库版本
        version_result = await conn.execute_query("SELECT version();")
        version = version_result[1][0][0] if version_result[1] else "Unknown"
        
        # 获取连接数
        connections_result = await conn.execute_query(
            "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"
        )
        active_connections = connections_result[1][0][0] if connections_result[1] else 0
        
        # 获取数据库大小
        size_result = await conn.execute_query(
            "SELECT pg_size_pretty(pg_database_size(current_database()));"
        )
        database_size = size_result[1][0][0] if size_result[1] else "Unknown"
        
        # 检查pgvector扩展
        vector_result = await conn.execute_query(
            "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector');"
        )
        vector_enabled = vector_result[1][0][0] if vector_result[1] else False
        
        # 获取表统计信息
        tables_result = await conn.execute_query("""
            SELECT 
                schemaname,
                tablename,
                n_tup_ins as inserts,
                n_tup_upd as updates,
                n_tup_del as deletes,
                n_live_tup as live_tuples
            FROM pg_stat_user_tables 
            WHERE schemaname = 'public'
            ORDER BY tablename;
        """)
        
        tables_stats = []
        if tables_result[1]:
            for row in tables_result[1]:
                tables_stats.append({
                    "schema": row[0],
                    "table": row[1],
                    "inserts": row[2],
                    "updates": row[3],
                    "deletes": row[4],
                    "live_tuples": row[5]
                })
        
        return {
            "status": "connected",
            "version": version,
            "active_connections": active_connections,
            "database_size": database_size,
            "vector_enabled": vector_enabled,
            "tables_stats": tables_stats
        }
        
    except Exception as e:
        logger.error(f"获取数据库状态失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


async def create_indexes() -> None:
    """创建额外的数据库索引"""
    try:
        conn = Tortoise.get_connection("default")
        
        # 为images表创建向量索引（如果支持pgvector）
        try:
            await conn.execute_query("""
                CREATE INDEX IF NOT EXISTS idx_images_features_ivfflat 
                ON images USING ivfflat(features vector_cosine_ops) 
                WITH (lists = 100);
            """)
            logger.info("已创建images表向量索引")
        except Exception as e:
            logger.warning(f"创建images表向量索引失败: {e}")
        
        # 为videos表创建GIN索引
        try:
            await conn.execute_query("""
                CREATE INDEX IF NOT EXISTS idx_videos_frames_data_gin 
                ON videos USING GIN(frames_data);
            """)
            logger.info("已创建videos表GIN索引")
        except Exception as e:
            logger.warning(f"创建videos表GIN索引失败: {e}")
        
        # 为metadata字段创建GIN索引
        try:
            await conn.execute_query("""
                CREATE INDEX IF NOT EXISTS idx_images_metadata_gin 
                ON images USING GIN(metadata);
            """)
            await conn.execute_query("""
                CREATE INDEX IF NOT EXISTS idx_videos_metadata_gin 
                ON videos USING GIN(metadata);
            """)
            logger.info("已创建metadata字段GIN索引")
        except Exception as e:
            logger.warning(f"创建metadata字段GIN索引失败: {e}")
            
    except Exception as e:
        logger.error(f"创建数据库索引失败: {e}")


async def cleanup_database() -> None:
    """清理数据库"""
    try:
        conn = Tortoise.get_connection("default")
        
        # 清理过期的任务记录
        await conn.execute_query("""
            DELETE FROM tasks 
            WHERE status IN ('COMPLETED', 'FAILED', 'CANCELLED') 
            AND created_at < NOW() - INTERVAL '30 days';
        """)
        
        # 清理孤立的文件记录
        await conn.execute_query("""
            DELETE FROM images 
            WHERE process_status = 'FAILED' 
            AND created_at < NOW() - INTERVAL '7 days';
        """)
        
        await conn.execute_query("""
            DELETE FROM videos 
            WHERE process_status = 'FAILED' 
            AND created_at < NOW() - INTERVAL '7 days';
        """)
        
        # 执行VACUUM和ANALYZE
        await conn.execute_query("VACUUM ANALYZE;")
        
        logger.info("数据库清理完成")
        
    except Exception as e:
        logger.error(f"数据库清理失败: {e}")
