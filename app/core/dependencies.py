"""
FastAPI依赖注入

提供全局依赖项，如配置、数据库连接等。
"""

from functools import lru_cache
from typing import Optional

from fastapi import Depends, HTTPException, status
from tortoise.exceptions import DoesNotExist

from app.config.settings import Settings, get_settings
from app.config.manager import ConfigManager
from app.utils.logger import get_logger
from app.utils.exceptions import MaterialSearchException
from app.utils.response import ErrorCode

logger = get_logger(__name__)

# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


@lru_cache()
def get_current_settings() -> Settings:
    """获取当前设置"""
    return get_settings()


async def get_config_manager() -> ConfigManager:
    """获取配置管理器"""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager()
        await _config_manager.initialize()
    
    return _config_manager


async def get_database_connection():
    """获取数据库连接（用于依赖注入）"""
    # 这里可以添加数据库连接的验证逻辑
    pass


def require_model_loaded():
    """要求模型已加载的依赖"""
    def dependency():
        # 这里可以检查模型是否已加载
        # 如果未加载，抛出异常
        pass
    return dependency


def validate_file_type(file_type: str):
    """验证文件类型"""
    allowed_types = ['image', 'video']
    if file_type.lower() not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {file_type}"
        )
    return file_type.lower()


def validate_search_params(
    query: Optional[str] = None,
    limit: int = 20,
    threshold: Optional[float] = None
):
    """验证搜索参数"""
    if not query or query.strip() == "":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="搜索查询不能为空"
        )
    
    if limit < 1 or limit > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="结果数量限制必须在1-100之间"
        )
    
    if threshold is not None and (threshold < 0 or threshold > 100):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="相似度阈值必须在0-100之间"
        )
    
    return {
        "query": query.strip(),
        "limit": limit,
        "threshold": threshold
    }


def validate_pagination(page: int = 1, page_size: int = 20):
    """验证分页参数"""
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="页码必须大于0"
        )
    
    if page_size < 1 or page_size > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="每页大小必须在1-100之间"
        )
    
    return {"page": page, "page_size": page_size}


async def get_image_by_id(image_id: str):
    """根据ID获取图片"""
    from app.models.database import Images
    
    try:
        image = await Images.get(id=image_id)
        return image
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"图片不存在: {image_id}"
        )
    except Exception as e:
        logger.error(f"获取图片失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取图片失败"
        )


async def get_video_by_id(video_id: str):
    """根据ID获取视频"""
    from app.models.database import Videos
    
    try:
        video = await Videos.get(id=video_id)
        return video
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"视频不存在: {video_id}"
        )
    except Exception as e:
        logger.error(f"获取视频失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取视频失败"
        )


async def get_task_by_id(task_id: str):
    """根据ID获取任务"""
    from app.models.database import Tasks
    
    try:
        task = await Tasks.get(id=task_id)
        return task
    except DoesNotExist:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"任务不存在: {task_id}"
        )
    except Exception as e:
        logger.error(f"获取任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务失败"
        )


def handle_database_error(func):
    """数据库错误处理装饰器"""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except DoesNotExist as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资源不存在"
            )
        except MaterialSearchException as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=e.message
            )
        except Exception as e:
            logger.error(f"数据库操作失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="数据库操作失败"
            )
    return wrapper


class CommonQueryParams:
    """通用查询参数"""
    
    def __init__(
        self,
        page: int = 1,
        page_size: int = 20,
        sort_by: Optional[str] = None,
        sort_order: str = "desc"
    ):
        self.page = max(1, page)
        self.page_size = min(max(1, page_size), 100)
        self.sort_by = sort_by
        self.sort_order = sort_order.lower() if sort_order.lower() in ["asc", "desc"] else "desc"
        
        # 计算偏移量
        self.offset = (self.page - 1) * self.page_size
        self.limit = self.page_size


class SearchQueryParams:
    """搜索查询参数"""
    
    def __init__(
        self,
        query: str,
        file_type: Optional[str] = None,
        business_type: Optional[str] = None,
        limit: int = 20,
        threshold: Optional[float] = None
    ):
        if not query or query.strip() == "":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="搜索查询不能为空"
            )
        
        self.query = query.strip()
        self.file_type = file_type
        self.business_type = business_type
        self.limit = min(max(1, limit), 100)
        self.threshold = threshold
        
        if self.threshold is not None:
            if self.threshold < 0 or self.threshold > 100:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="相似度阈值必须在0-100之间"
                )


# 依赖项别名
CurrentSettings = Depends(get_current_settings)
ConfigManager = Depends(get_config_manager)
DatabaseConnection = Depends(get_database_connection)
CommonQuery = Depends(CommonQueryParams)
SearchQuery = Depends(SearchQueryParams)
