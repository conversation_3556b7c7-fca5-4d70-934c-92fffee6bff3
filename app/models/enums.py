"""
枚举类型定义

定义系统中使用的各种枚举类型。
"""

from enum import Enum


class ContentSourceType(str, Enum):
    """内容来源类型"""
    SCAN = "SCAN"        # 目录扫描获得
    UPLOAD = "UPLOAD"    # 用户上传
    API = "API"          # API接口添加


class ProcessStatus(str, Enum):
    """处理状态"""
    PENDING = "PENDING"        # 等待处理
    PROCESSING = "PROCESSING"  # 正在处理
    COMPLETED = "COMPLETED"    # 处理完成
    FAILED = "FAILED"          # 处理失败


class TaskType(str, Enum):
    """任务类型"""
    SINGLE_FILE = "SINGLE_FILE"        # 单文件处理
    BATCH_FILES = "BATCH_FILES"        # 批量文件处理
    DIRECTORY_SCAN = "DIRECTORY_SCAN"  # 目录扫描


class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "PENDING"      # 等待开始
    RUNNING = "RUNNING"      # 正在执行
    COMPLETED = "COMPLETED"  # 执行完成
    FAILED = "FAILED"        # 执行失败
    CANCELLED = "CANCELLED"  # 已取消
    LOCKED = "LOCKED"        # 已锁定（防止多进程重复处理）


class SearchType(str, Enum):
    """搜索类型"""
    VISUAL_ONLY = "VISUAL_ONLY"        # 纯视觉搜索
    ATTRIBUTE_ONLY = "ATTRIBUTE_ONLY"  # 纯属性搜索
    COMBINED = "COMBINED"              # 组合搜索
    WEIGHTED = "WEIGHTED"              # 加权搜索


class FileType(str, Enum):
    """文件类型"""
    IMAGE = "IMAGE"  # 图片
    VIDEO = "VIDEO"  # 视频


class ModelBackend(str, Enum):
    """模型后端"""
    TRANSFORMERS = "transformers"  # Hugging Face Transformers
    ONNX = "onnx"                 # ONNX Runtime
    OPENVINO = "openvino"         # Intel OpenVINO


class ModelPrecision(str, Enum):
    """模型精度"""
    FP32 = "fp32"  # 32位浮点
    FP16 = "fp16"  # 16位浮点
    INT8 = "int8"  # 8位整数


class DeviceType(str, Enum):
    """设备类型"""
    AUTO = "auto"  # 自动选择
    CPU = "cpu"    # CPU
    CUDA = "cuda"  # NVIDIA GPU
    MPS = "mps"    # Apple Metal Performance Shaders


class LogLevel(str, Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogFormat(str, Enum):
    """日志格式"""
    JSON = "json"  # JSON格式
    TEXT = "text"  # 文本格式


class Environment(str, Enum):
    """运行环境"""
    DEVELOPMENT = "development"  # 开发环境
    TESTING = "testing"         # 测试环境
    STAGING = "staging"         # 预发布环境
    PRODUCTION = "production"   # 生产环境
