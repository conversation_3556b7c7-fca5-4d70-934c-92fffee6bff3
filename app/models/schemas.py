"""
Pydantic模式定义

定义API请求和响应的数据模式。
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import BaseModel, Field, validator

from app.models.enums import (
    ContentSourceType,
    ProcessStatus,
    TaskType,
    TaskStatus,
    SearchType,
    FileType
)


class BaseSchema(BaseModel):
    """基础模式"""
    
    class Config:
        from_attributes = True
        use_enum_values = True


# 图片相关模式
class ImageBase(BaseSchema):
    """图片基础模式"""
    path: str = Field(description="文件路径")
    filename: str = Field(description="文件名")
    file_size: Optional[int] = Field(None, description="文件大小")
    width: Optional[int] = Field(None, description="图片宽度")
    height: Optional[int] = Field(None, description="图片高度")
    format: Optional[str] = Field(None, description="图片格式")
    source_type: ContentSourceType = Field(description="来源类型")
    business_type: Optional[str] = Field(None, description="业务类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")


class ImageCreate(ImageBase):
    """创建图片模式"""
    pass


class ImageUpdate(BaseSchema):
    """更新图片模式"""
    business_type: Optional[str] = Field(None, description="业务类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")


class ImageResponse(ImageBase):
    """图片响应模式"""
    id: UUID = Field(description="图片ID")
    checksum: Optional[str] = Field(None, description="文件校验和")
    process_status: ProcessStatus = Field(description="处理状态")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    processed_at: Optional[datetime] = Field(None, description="处理完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")


# 视频相关模式
class VideoBase(BaseSchema):
    """视频基础模式"""
    path: str = Field(description="文件路径")
    filename: str = Field(description="文件名")
    file_size: Optional[int] = Field(None, description="文件大小")
    duration: Optional[float] = Field(None, description="视频时长")
    fps: Optional[float] = Field(None, description="帧率")
    width: Optional[int] = Field(None, description="视频宽度")
    height: Optional[int] = Field(None, description="视频高度")
    format: Optional[str] = Field(None, description="视频格式")
    frame_count: Optional[int] = Field(None, description="总帧数")
    source_type: ContentSourceType = Field(description="来源类型")
    business_type: Optional[str] = Field(None, description="业务类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")


class VideoCreate(VideoBase):
    """创建视频模式"""
    pass


class VideoUpdate(BaseSchema):
    """更新视频模式"""
    business_type: Optional[str] = Field(None, description="业务类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")


class VideoResponse(VideoBase):
    """视频响应模式"""
    id: UUID = Field(description="视频ID")
    checksum: Optional[str] = Field(None, description="文件校验和")
    frames_data: Optional[Dict[str, Any]] = Field(None, description="帧数据")
    process_status: ProcessStatus = Field(description="处理状态")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    processed_at: Optional[datetime] = Field(None, description="处理完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")


# 任务相关模式
class TaskBase(BaseSchema):
    """任务基础模式"""
    task_type: TaskType = Field(description="任务类型")
    created_by: Optional[str] = Field(None, description="创建用户")
    metadata: Optional[Dict[str, Any]] = Field(None, description="任务元数据")


class TaskCreate(TaskBase):
    """创建任务模式"""
    pass


class TaskUpdate(BaseSchema):
    """更新任务模式"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    progress: Optional[float] = Field(None, ge=0, le=100, description="进度百分比")
    error_message: Optional[str] = Field(None, description="错误信息")


class TaskResponse(TaskBase):
    """任务响应模式"""
    id: UUID = Field(description="任务ID")
    status: TaskStatus = Field(description="任务状态")
    total_files: int = Field(description="总文件数")
    processed_files: int = Field(description="已处理文件数")
    failed_files: int = Field(description="失败文件数")
    progress: float = Field(description="进度百分比")
    created_at: datetime = Field(description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    retry_count: int = Field(description="重试次数")
    max_retries: int = Field(description="最大重试次数")
    error_message: Optional[str] = Field(None, description="错误信息")


# 搜索相关模式
class SearchRequest(BaseSchema):
    """搜索请求模式"""
    query: str = Field(description="搜索查询")
    search_type: SearchType = Field(default=SearchType.VISUAL_ONLY, description="搜索类型")
    file_type: Optional[FileType] = Field(None, description="文件类型过滤")
    business_type: Optional[str] = Field(None, description="业务类型过滤")
    limit: int = Field(default=20, ge=1, le=100, description="结果数量限制")
    threshold: Optional[float] = Field(None, ge=0, le=100, description="相似度阈值")
    metadata_filter: Optional[Dict[str, Any]] = Field(None, description="元数据过滤条件")


class SearchResult(BaseSchema):
    """搜索结果模式"""
    id: UUID = Field(description="文件ID")
    path: str = Field(description="文件路径")
    filename: str = Field(description="文件名")
    file_type: FileType = Field(description="文件类型")
    similarity: float = Field(description="相似度分数")
    business_type: Optional[str] = Field(None, description="业务类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")
    thumbnail_url: Optional[str] = Field(None, description="缩略图URL")


class SearchResponse(BaseSchema):
    """搜索响应模式"""
    query: str = Field(description="搜索查询")
    search_type: SearchType = Field(description="搜索类型")
    total_results: int = Field(description="结果总数")
    search_time: float = Field(description="搜索耗时(秒)")
    results: List[SearchResult] = Field(description="搜索结果")


# 文件上传相关模式
class FileUploadRequest(BaseSchema):
    """文件上传请求模式"""
    business_type: Optional[str] = Field(None, description="业务类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")
    process_immediately: bool = Field(default=True, description="是否立即处理")


class FileUploadResponse(BaseSchema):
    """文件上传响应模式"""
    file_id: UUID = Field(description="文件ID")
    filename: str = Field(description="文件名")
    file_size: int = Field(description="文件大小")
    file_type: FileType = Field(description="文件类型")
    upload_time: datetime = Field(description="上传时间")
    processing_status: ProcessStatus = Field(description="处理状态")


# 批量操作模式
class BatchProcessRequest(BaseSchema):
    """批量处理请求模式"""
    file_paths: List[str] = Field(description="文件路径列表")
    business_type: Optional[str] = Field(None, description="业务类型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展属性")
    
    @validator('file_paths')
    def validate_file_paths(cls, v):
        if not v:
            raise ValueError('文件路径列表不能为空')
        if len(v) > 1000:
            raise ValueError('单次批量处理文件数量不能超过1000个')
        return v


class BatchProcessResponse(BaseSchema):
    """批量处理响应模式"""
    task_id: UUID = Field(description="任务ID")
    total_files: int = Field(description="总文件数")
    message: str = Field(description="响应消息")


# 系统状态模式
class SystemStatus(BaseSchema):
    """系统状态模式"""
    status: str = Field(description="系统状态")
    version: str = Field(description="系统版本")
    uptime: float = Field(description="运行时间(秒)")
    memory_usage: Dict[str, Any] = Field(description="内存使用情况")
    disk_usage: Dict[str, Any] = Field(description="磁盘使用情况")
    database_status: str = Field(description="数据库状态")
    model_status: str = Field(description="模型状态")
    statistics: Dict[str, Any] = Field(description="统计信息")


# 配置相关模式
class ConfigUpdateRequest(BaseSchema):
    """配置更新请求模式"""
    value: Any = Field(description="配置值")
    reason: Optional[str] = Field(None, description="变更原因")


class ConfigSectionUpdateRequest(BaseSchema):
    """配置节更新请求模式"""
    config: Dict[str, Any] = Field(description="配置数据")
    reason: Optional[str] = Field(None, description="变更原因")


class ConfigReloadRequest(BaseSchema):
    """配置重载请求模式"""
    section: Optional[str] = Field(None, description="配置节名称")
    reason: Optional[str] = Field(None, description="重载原因")


class ConfigResetRequest(BaseSchema):
    """配置重置请求模式"""
    section: Optional[str] = Field(None, description="配置节名称")
    key: Optional[str] = Field(None, description="配置键名")
    reason: Optional[str] = Field(None, description="重置原因")
