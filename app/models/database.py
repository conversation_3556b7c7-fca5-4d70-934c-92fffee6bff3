"""
数据库模型定义

使用Tortoise ORM定义数据库表结构。
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List

from tortoise.models import Model
from tortoise import fields

from app.models.enums import (
    ContentSourceType,
    ProcessStatus,
    TaskType,
    TaskStatus
)


class BaseModel(Model):
    """基础模型类"""
    
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        abstract = True


class Images(BaseModel):
    """图片表模型"""
    
    # 基本信息
    path = fields.CharField(max_length=1000, unique=True, description="文件路径")
    filename = fields.CharField(max_length=255, description="文件名")
    file_size = fields.BigIntField(null=True, description="文件大小(字节)")
    checksum = fields.CharField(max_length=64, null=True, description="文件校验和")
    
    # 图片属性
    width = fields.IntField(null=True, description="图片宽度")
    height = fields.IntField(null=True, description="图片高度")
    format = fields.CharField(max_length=10, null=True, description="图片格式")
    
    # AI特征
    features = fields.JSONField(null=True, description="特征向量")
    
    # 分类信息
    source_type = fields.CharEnumField(ContentSourceType, description="来源类型")
    business_type = fields.CharField(max_length=50, null=True, description="业务类型")
    
    # 处理状态
    process_status = fields.CharEnumField(
        ProcessStatus, 
        default=ProcessStatus.PENDING, 
        description="处理状态"
    )
    processed_at = fields.DatetimeField(null=True, description="处理完成时间")
    error_message = fields.TextField(null=True, description="错误信息")
    
    # 扩展属性
    metadata = fields.JSONField(null=True, description="扩展属性")
    
    class Meta:
        table = "images"
        indexes = [
            ("path",),
            ("source_type",),
            ("process_status",),
            ("business_type",),
            ("created_at",),
        ]
    
    def __str__(self) -> str:
        return f"Image({self.filename})"
    
    @property
    def is_processed(self) -> bool:
        """是否已处理完成"""
        return self.process_status == ProcessStatus.COMPLETED
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": str(self.id),
            "path": self.path,
            "filename": self.filename,
            "file_size": self.file_size,
            "width": self.width,
            "height": self.height,
            "format": self.format,
            "source_type": self.source_type,
            "business_type": self.business_type,
            "process_status": self.process_status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "metadata": self.metadata,
        }


class Videos(BaseModel):
    """视频表模型"""
    
    # 基本信息
    path = fields.CharField(max_length=1000, unique=True, description="视频文件路径")
    filename = fields.CharField(max_length=255, description="文件名")
    file_size = fields.BigIntField(null=True, description="文件大小(字节)")
    checksum = fields.CharField(max_length=64, null=True, description="文件校验和")
    
    # 视频属性
    duration = fields.FloatField(null=True, description="视频总时长(秒)")
    fps = fields.FloatField(null=True, description="帧率")
    width = fields.IntField(null=True, description="视频宽度")
    height = fields.IntField(null=True, description="视频高度")
    format = fields.CharField(max_length=10, null=True, description="视频格式")
    frame_count = fields.IntField(null=True, description="总帧数")
    
    # 帧数据
    frames_data = fields.JSONField(null=True, description="帧数据(包含时间戳、特征向量等)")
    
    # 分类信息
    source_type = fields.CharEnumField(ContentSourceType, description="来源类型")
    business_type = fields.CharField(max_length=50, null=True, description="业务类型")
    
    # 处理状态
    process_status = fields.CharEnumField(
        ProcessStatus, 
        default=ProcessStatus.PENDING, 
        description="处理状态"
    )
    processed_at = fields.DatetimeField(null=True, description="处理完成时间")
    error_message = fields.TextField(null=True, description="错误信息")
    
    # 扩展属性
    metadata = fields.JSONField(null=True, description="扩展属性")
    
    class Meta:
        table = "videos"
        indexes = [
            ("path",),
            ("source_type",),
            ("process_status",),
            ("business_type",),
            ("created_at",),
        ]
    
    def __str__(self) -> str:
        return f"Video({self.filename})"
    
    @property
    def is_processed(self) -> bool:
        """是否已处理完成"""
        return self.process_status == ProcessStatus.COMPLETED
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": str(self.id),
            "path": self.path,
            "filename": self.filename,
            "file_size": self.file_size,
            "duration": self.duration,
            "fps": self.fps,
            "width": self.width,
            "height": self.height,
            "format": self.format,
            "frame_count": self.frame_count,
            "source_type": self.source_type,
            "business_type": self.business_type,
            "process_status": self.process_status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "metadata": self.metadata,
        }


class Tasks(BaseModel):
    """任务表模型"""
    
    # 任务基本信息
    task_type = fields.CharEnumField(TaskType, description="任务类型")
    status = fields.CharEnumField(TaskStatus, default=TaskStatus.PENDING, description="任务状态")
    
    # 进度信息
    total_files = fields.IntField(default=0, description="总文件数")
    processed_files = fields.IntField(default=0, description="已处理文件数")
    failed_files = fields.IntField(default=0, description="失败文件数")
    progress = fields.FloatField(default=0.0, description="进度百分比")
    
    # 时间信息
    started_at = fields.DatetimeField(null=True, description="开始时间")
    completed_at = fields.DatetimeField(null=True, description="完成时间")
    
    # 锁定机制
    locked_at = fields.DatetimeField(null=True, description="锁定时间")
    locked_by = fields.CharField(max_length=100, null=True, description="锁定进程ID")
    
    # 重试机制
    retry_count = fields.IntField(default=0, description="重试次数")
    max_retries = fields.IntField(default=3, description="最大重试次数")
    
    # 错误信息
    error_message = fields.TextField(null=True, description="错误信息")
    
    # 创建者
    created_by = fields.CharField(max_length=100, null=True, description="创建用户")
    
    # 任务元数据
    metadata = fields.JSONField(null=True, description="任务元数据")
    
    class Meta:
        table = "tasks"
        indexes = [
            ("status",),
            ("task_type",),
            ("locked_at",),
            ("created_at",),
        ]
    
    def __str__(self) -> str:
        return f"Task({self.task_type}-{self.status})"
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self.status == TaskStatus.RUNNING
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
    
    @property
    def is_locked(self) -> bool:
        """是否被锁定"""
        return self.locked_at is not None and self.status == TaskStatus.LOCKED
    
    @property
    def can_retry(self) -> bool:
        """是否可以重试"""
        return self.retry_count < self.max_retries and self.status == TaskStatus.FAILED
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": str(self.id),
            "task_type": self.task_type,
            "status": self.status,
            "total_files": self.total_files,
            "processed_files": self.processed_files,
            "failed_files": self.failed_files,
            "progress": self.progress,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "error_message": self.error_message,
            "created_by": self.created_by,
            "metadata": self.metadata,
        }
