"""
应用配置设置

使用Pydantic进行配置验证和类型检查。
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = Field(default="127.0.0.1", description="服务器主机地址")
    port: int = Field(default=8085, ge=1, le=65535, description="服务器端口")
    debug: bool = Field(default=False, description="调试模式")
    workers: int = Field(default=1, ge=1, description="工作进程数")
    reload: bool = Field(default=False, description="自动重载")


class DatabaseConfig(BaseModel):
    """数据库配置"""
    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=5432, ge=1, le=65535, description="数据库端口")
    name: str = Field(description="数据库名称")
    user: str = Field(description="数据库用户名")
    password: str = Field(description="数据库密码")
    pool_size: int = Field(default=20, ge=1, description="连接池大小")
    vector_dimension: int = Field(default=512, description="向量维度")
    timeout: int = Field(default=30, ge=1, description="连接超时时间")

    @property
    def url(self) -> str:
        """获取数据库连接URL"""
        return f"postgres://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class ModelConfig(BaseModel):
    """模型配置"""
    name: str = Field(default="OFA-Sys/chinese-clip-vit-base-patch16", description="模型名称")
    backend: str = Field(default="transformers", description="推理后端")
    precision: str = Field(default="fp32", description="模型精度")
    device: str = Field(default="auto", description="计算设备")
    cache_dir: str = Field(default="./models", description="模型缓存目录")
    batch_size: int = Field(default=4, ge=1, description="批处理大小")

    @validator('backend')
    def validate_backend(cls, v):
        allowed = ['transformers', 'onnx', 'openvino']
        if v not in allowed:
            raise ValueError(f'backend必须是{allowed}中的一个')
        return v

    @validator('precision')
    def validate_precision(cls, v):
        allowed = ['fp32', 'fp16', 'int8']
        if v not in allowed:
            raise ValueError(f'precision必须是{allowed}中的一个')
        return v

    @validator('device')
    def validate_device(cls, v):
        allowed = ['auto', 'cpu', 'cuda', 'mps']
        if v not in allowed:
            raise ValueError(f'device必须是{allowed}中的一个')
        return v


class ScanConfig(BaseModel):
    """扫描配置"""
    assets_paths: List[str] = Field(default_factory=lambda: ["/data/images", "/data/videos"], description="资源路径")
    skip_paths: List[str] = Field(default_factory=lambda: ["/tmp", "/var/cache"], description="跳过路径")
    image_extensions: List[str] = Field(
        default_factory=lambda: [".jpg", ".jpeg", ".png", ".gif", ".heic", ".webp", ".bmp"],
        description="图片扩展名"
    )
    video_extensions: List[str] = Field(
        default_factory=lambda: [".mp4", ".flv", ".mov", ".mkv", ".webm", ".avi"],
        description="视频扩展名"
    )
    frame_interval: int = Field(default=2, ge=1, description="视频帧间隔")
    image_min_width: int = Field(default=64, ge=1, description="图片最小宽度")
    image_min_height: int = Field(default=64, ge=1, description="图片最小高度")
    auto_save_interval: int = Field(default=100, ge=1, description="自动保存间隔")


class SearchConfig(BaseModel):
    """搜索配置"""
    cache_size: int = Field(default=64, ge=1, description="缓存大小")
    positive_threshold: int = Field(default=36, ge=0, le=100, description="正向阈值")
    negative_threshold: int = Field(default=36, ge=0, le=100, description="负向阈值")
    image_threshold: int = Field(default=85, ge=0, le=100, description="图片阈值")


class TaskConfig(BaseModel):
    """任务配置"""
    sync_process_timeout: int = Field(default=30, ge=1, description="同步处理超时")
    task_lock_timeout: int = Field(default=30, ge=1, description="任务锁超时")
    max_retry_attempts: int = Field(default=3, ge=0, description="最大重试次数")
    cleanup_interval: int = Field(default=24, ge=1, description="清理间隔(小时)")
    background_workers: int = Field(default=4, ge=1, description="后台工作进程数")
    realtime_enabled: bool = Field(default=True, description="实时处理启用")
    max_concurrent_uploads: int = Field(default=10, ge=1, description="最大并发上传数")
    upload_timeout: int = Field(default=300, ge=1, description="上传超时时间")
    auto_async_threshold: int = Field(default=10485760, ge=0, description="自动异步阈值(字节)")


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(default="json", description="日志格式")
    file_path: str = Field(default="./logs/app.log", description="日志文件路径")
    max_size: int = Field(default=100, ge=1, description="日志文件最大大小(MB)")
    backup_count: int = Field(default=5, ge=0, description="备份文件数量")
    rotation: str = Field(default="size", description="轮转方式")
    console_enabled: bool = Field(default=True, description="控制台输出启用")
    file_enabled: bool = Field(default=True, description="文件输出启用")

    @validator('level')
    def validate_level(cls, v):
        allowed = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in allowed:
            raise ValueError(f'level必须是{allowed}中的一个')
        return v.upper()

    @validator('format')
    def validate_format(cls, v):
        allowed = ['json', 'text']
        if v not in allowed:
            raise ValueError(f'format必须是{allowed}中的一个')
        return v


class AppConfig(BaseModel):
    """应用配置"""
    name: str = Field(default="MaterialSearch", description="应用名称")
    version: str = Field(default="2.0.0", description="应用版本")
    description: str = Field(default="AI-powered local media search system", description="应用描述")


class Settings(BaseSettings):
    """应用设置"""
    
    # 环境配置
    environment: str = Field(default="development", description="运行环境")
    
    # 各模块配置
    app: AppConfig = Field(default_factory=AppConfig)
    server: ServerConfig = Field(default_factory=ServerConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    model: ModelConfig = Field(default_factory=ModelConfig)
    scan: ScanConfig = Field(default_factory=ScanConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)
    task: TaskConfig = Field(default_factory=TaskConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_nested_delimiter = "__"

    @validator('environment')
    def validate_environment(cls, v):
        allowed = ['development', 'testing', 'staging', 'production']
        if v not in allowed:
            raise ValueError(f'environment必须是{allowed}中的一个')
        return v


@lru_cache()
def get_settings() -> Settings:
    """获取应用设置单例"""
    return Settings()
