"""
配置加载器

支持多环境配置文件加载和环境变量替换。
"""

import os
import re
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

from app.utils.logger import get_logger

logger = get_logger(__name__)


class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.environment = os.getenv("ENVIRONMENT", "development")
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            # 1. 加载基础配置
            base_config = self._load_yaml("base.yaml")
            logger.info("已加载基础配置")
            
            # 2. 加载环境特定配置
            env_config = self._load_yaml(f"{self.environment}.yaml")
            logger.info(f"已加载{self.environment}环境配置")
            
            # 3. 加载本地配置（如果存在）
            local_config = self._load_yaml("local.yaml", required=False)
            if local_config:
                logger.info("已加载本地配置")
            
            # 4. 合并配置
            config = self._merge_configs(base_config, env_config, local_config)
            
            # 5. 环境变量替换
            config = self._substitute_env_vars(config)
            
            logger.info("配置加载完成")
            return config
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    def _load_yaml(self, filename: str, required: bool = True) -> Dict[str, Any]:
        """加载YAML文件"""
        file_path = self.config_dir / filename
        
        if not file_path.exists():
            if required:
                raise FileNotFoundError(f"配置文件不存在: {file_path}")
            return {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = yaml.safe_load(f)
                return content or {}
        except yaml.YAMLError as e:
            raise ValueError(f"YAML文件格式错误 {file_path}: {e}")
        except Exception as e:
            raise IOError(f"读取配置文件失败 {file_path}: {e}")
    
    def _merge_configs(self, *configs) -> Dict[str, Any]:
        """深度合并配置"""
        result = {}
        
        for config in configs:
            if config:
                result = self._deep_merge(result, config)
        
        return result
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并两个字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """替换环境变量"""
        def substitute_value(value):
            if isinstance(value, str):
                # 匹配 ${VAR_NAME} 或 ${VAR_NAME:default_value} 格式
                pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
                
                def replace_var(match):
                    var_name = match.group(1)
                    default_value = match.group(2) if match.group(2) is not None else ""
                    return os.getenv(var_name, default_value)
                
                return re.sub(pattern, replace_var, value)
            elif isinstance(value, dict):
                return {k: substitute_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [substitute_value(item) for item in value]
            else:
                return value
        
        return substitute_value(config)
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置的完整性"""
        required_sections = ['server', 'database', 'model', 'logging']
        
        for section in required_sections:
            if section not in config:
                logger.error(f"缺少必需的配置节: {section}")
                return False
        
        # 验证数据库配置
        db_config = config.get('database', {})
        required_db_fields = ['host', 'port', 'name', 'user', 'password']
        
        for field in required_db_fields:
            if not db_config.get(field):
                logger.error(f"数据库配置缺少必需字段: {field}")
                return False
        
        logger.info("配置验证通过")
        return True
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "config_dir": str(self.config_dir),
            "environment": self.environment,
            "config_files": {
                "base": (self.config_dir / "base.yaml").exists(),
                "environment": (self.config_dir / f"{self.environment}.yaml").exists(),
                "local": (self.config_dir / "local.yaml").exists(),
            }
        }
