"""
配置管理器

提供动态配置管理、配置热更新等功能。
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from app.config.loader import ConfigLoader
from app.utils.logger import get_logger

logger = get_logger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.loader = ConfigLoader(config_dir)
        self.static_config = {}
        self.dynamic_config = {}
        self.config_schema = {}
        self.dynamic_updatable_keys = set()
        self._load_config_schema()
        self._load_dynamic_keys()
    
    async def initialize(self):
        """初始化配置管理器"""
        try:
            # 加载静态配置
            self.static_config = self.loader.load_config()
            
            # 加载动态配置
            await self._load_dynamic_config()
            
            logger.info("配置管理器初始化完成")
        except Exception as e:
            logger.error(f"配置管理器初始化失败: {e}")
            raise
    
    def get_value(self, section: str, key: str) -> Any:
        """获取配置值（动态配置优先）"""
        # 1. 检查动态配置
        dynamic_key = f"{section}.{key}"
        if dynamic_key in self.dynamic_config:
            return self.dynamic_config[dynamic_key]
        
        # 2. 检查静态配置
        if section in self.static_config and key in self.static_config[section]:
            return self.static_config[section][key]
        
        return None
    
    def get_section(self, section: str) -> Optional[Dict[str, Any]]:
        """获取配置节"""
        if section not in self.static_config:
            return None
        
        result = self.static_config[section].copy()
        
        # 覆盖动态配置
        for dynamic_key, value in self.dynamic_config.items():
            if dynamic_key.startswith(f"{section}."):
                key = dynamic_key[len(f"{section}."):]
                result[key] = value
        
        return result
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        result = {}
        
        for section_name, section_config in self.static_config.items():
            result[section_name] = section_config.copy()
        
        # 覆盖动态配置
        for dynamic_key, value in self.dynamic_config.items():
            if "." in dynamic_key:
                section, key = dynamic_key.split(".", 1)
                if section not in result:
                    result[section] = {}
                result[section][key] = value
        
        return result
    
    def set_value(self, section: str, key: str, value: Any):
        """设置动态配置值"""
        dynamic_key = f"{section}.{key}"
        old_value = self.get_value(section, key)
        
        self.dynamic_config[dynamic_key] = value
        
        # 持久化到文件
        self._save_dynamic_config(dynamic_key, value)
        
        # 触发配置变更事件
        asyncio.create_task(self._notify_config_change(section, key, old_value, value))
        
        logger.info(f"配置已更新: {dynamic_key} = {value}")
    
    def update_section(self, section: str, config: Dict[str, Any]):
        """批量更新配置节"""
        for key, value in config.items():
            self.set_value(section, key, value)
    
    def is_dynamic_updatable(self, section: str, key: str) -> bool:
        """检查配置项是否支持动态更新"""
        return f"{section}.{key}" in self.dynamic_updatable_keys
    
    def validate_config_value(self, section: str, key: str, value: Any) -> bool:
        """验证配置值的有效性"""
        schema_key = f"{section}.{key}"
        if schema_key not in self.config_schema:
            return True  # 如果没有schema定义，则认为有效
        
        schema = self.config_schema[schema_key]
        
        try:
            # 类型检查
            expected_type = schema.get("type")
            if expected_type:
                if expected_type == "int" and not isinstance(value, int):
                    return False
                elif expected_type == "float" and not isinstance(value, (int, float)):
                    return False
                elif expected_type == "str" and not isinstance(value, str):
                    return False
                elif expected_type == "bool" and not isinstance(value, bool):
                    return False
                elif expected_type == "list" and not isinstance(value, list):
                    return False
                elif expected_type == "dict" and not isinstance(value, dict):
                    return False
            
            # 范围检查
            if "min_value" in schema and value < schema["min_value"]:
                return False
            if "max_value" in schema and value > schema["max_value"]:
                return False
            
            # 枚举检查
            if "allowed_values" in schema and value not in schema["allowed_values"]:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"配置值验证失败: {e}")
            return False
    
    def has_section(self, section: str) -> bool:
        """检查配置节是否存在"""
        return section in self.static_config
    
    def reload_section(self, section: str):
        """重新加载指定配置节"""
        try:
            new_config = self.loader.load_config()
            if section in new_config:
                self.static_config[section] = new_config[section]
                logger.info(f"配置节 {section} 重载完成")
            else:
                logger.warning(f"配置节 {section} 不存在")
        except Exception as e:
            logger.error(f"重载配置节 {section} 失败: {e}")
            raise
    
    def reload_all(self):
        """重新加载所有配置"""
        try:
            self.static_config = self.loader.load_config()
            logger.info("所有配置重载完成")
        except Exception as e:
            logger.error(f"重载所有配置失败: {e}")
            raise
    
    def reset_value(self, section: str, key: str):
        """重置配置项到默认值"""
        dynamic_key = f"{section}.{key}"
        if dynamic_key in self.dynamic_config:
            del self.dynamic_config[dynamic_key]
            self._remove_dynamic_config(dynamic_key)
            logger.info(f"配置项 {dynamic_key} 已重置")
    
    def reset_section(self, section: str):
        """重置配置节到默认值"""
        keys_to_remove = [k for k in self.dynamic_config.keys() if k.startswith(f"{section}.")]
        for key in keys_to_remove:
            del self.dynamic_config[key]
            self._remove_dynamic_config(key)
        logger.info(f"配置节 {section} 已重置")
    
    def reset_all_dynamic(self):
        """重置所有动态配置"""
        self.dynamic_config.clear()
        self._clear_all_dynamic_config()
        logger.info("所有动态配置已重置")
    
    def _load_config_schema(self):
        """加载配置schema"""
        schema_file = self.config_dir / "schema.json"
        if schema_file.exists():
            try:
                with open(schema_file, 'r', encoding='utf-8') as f:
                    self.config_schema = json.load(f)
                logger.info("配置schema加载完成")
            except Exception as e:
                logger.warning(f"加载配置schema失败: {e}")
    
    def _load_dynamic_keys(self):
        """加载支持动态更新的配置键"""
        # 定义支持动态更新的配置项
        self.dynamic_updatable_keys = {
            "search.cache_size",
            "search.positive_threshold",
            "search.negative_threshold",
            "search.image_threshold",
            "task.background_workers",
            "task.max_concurrent_uploads",
            "task.upload_timeout",
            "logging.level",
            "logging.console_enabled",
            "logging.file_enabled",
            "model.batch_size",
            "scan.frame_interval",
            "scan.auto_save_interval",
        }
    
    async def _load_dynamic_config(self):
        """从持久化存储加载动态配置"""
        # 这里可以从数据库或文件加载动态配置
        # 暂时使用文件存储
        dynamic_config_file = self.config_dir / "dynamic_config.json"
        if dynamic_config_file.exists():
            try:
                with open(dynamic_config_file, 'r', encoding='utf-8') as f:
                    self.dynamic_config = json.load(f)
                logger.info("动态配置加载完成")
            except Exception as e:
                logger.warning(f"加载动态配置失败: {e}")
    
    def _save_dynamic_config(self, key: str, value: Any):
        """保存动态配置到持久化存储"""
        try:
            dynamic_config_file = self.config_dir / "dynamic_config.json"
            dynamic_config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(dynamic_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.dynamic_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存动态配置失败: {e}")
    
    def _remove_dynamic_config(self, key: str):
        """从持久化存储移除动态配置"""
        self._save_dynamic_config(key, None)  # 重新保存整个配置
    
    def _clear_all_dynamic_config(self):
        """清除所有动态配置"""
        try:
            dynamic_config_file = self.config_dir / "dynamic_config.json"
            if dynamic_config_file.exists():
                dynamic_config_file.unlink()
        except Exception as e:
            logger.error(f"清除动态配置失败: {e}")
    
    async def _notify_config_change(self, section: str, key: str, old_value: Any, new_value: Any):
        """通知配置变更"""
        try:
            # 这里可以发送WebSocket通知或触发其他事件
            logger.info(f"配置变更通知: {section}.{key} {old_value} -> {new_value}")
        except Exception as e:
            logger.error(f"发送配置变更通知失败: {e}")
