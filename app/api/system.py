"""
系统管理API路由

提供系统状态、健康检查等功能的API端点。
"""

import os
import psutil
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends

from app.models.schemas import SystemStatus
from app.utils.response import ApiResponse, ErrorCode
from app.core.dependencies import CurrentSettings
from app.core.database import get_database_status
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

# 系统启动时间
_start_time = datetime.now()


@router.get("/status", response_model=ApiResponse[SystemStatus])
async def get_system_status(
    settings = CurrentSettings
) -> ApiResponse[SystemStatus]:
    """获取系统状态
    
    返回系统运行状态、资源使用情况等信息。
    """
    try:
        # 计算运行时间
        uptime = (datetime.now() - _start_time).total_seconds()
        
        # 获取内存使用情况
        memory = psutil.virtual_memory()
        memory_usage = {
            "total": memory.total,
            "available": memory.available,
            "used": memory.used,
            "percentage": memory.percent
        }
        
        # 获取磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_usage = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percentage": (disk.used / disk.total) * 100
        }
        
        # 获取数据库状态
        db_status = await get_database_status()
        
        # 模拟模型状态
        model_status = "loaded"  # TODO: 实际检查模型状态
        
        # 获取统计信息
        statistics = await get_system_statistics()
        
        system_status = SystemStatus(
            status="running",
            version=settings.app.version,
            uptime=uptime,
            memory_usage=memory_usage,
            disk_usage=disk_usage,
            database_status=db_status.get("status", "unknown"),
            model_status=model_status,
            statistics=statistics
        )
        
        return ApiResponse.success(
            data=system_status,
            message="获取系统状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.SYSTEM_ERROR,
            message=f"获取系统状态失败: {str(e)}"
        )


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """健康检查
    
    简单的健康检查端点，用于负载均衡器等。
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "uptime": (datetime.now() - _start_time).total_seconds()
    }


@router.get("/info", response_model=ApiResponse[dict])
async def get_system_info(
    settings = CurrentSettings
) -> ApiResponse[dict]:
    """获取系统信息
    
    返回系统的详细信息。
    """
    try:
        # 获取系统信息
        system_info = {
            "app": {
                "name": settings.app.name,
                "version": settings.app.version,
                "description": settings.app.description
            },
            "environment": settings.environment,
            "python_version": f"{psutil.sys.version_info.major}.{psutil.sys.version_info.minor}.{psutil.sys.version_info.micro}",
            "platform": psutil.platform.platform(),
            "cpu_count": psutil.cpu_count(),
            "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat(),
            "start_time": _start_time.isoformat()
        }
        
        return ApiResponse.success(
            data=system_info,
            message="获取系统信息成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.SYSTEM_ERROR,
            message=f"获取系统信息失败: {str(e)}"
        )


@router.get("/metrics", response_model=ApiResponse[dict])
async def get_system_metrics(
    settings = CurrentSettings
) -> ApiResponse[dict]:
    """获取系统指标
    
    返回详细的系统性能指标。
    """
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # 内存信息
        memory = psutil.virtual_memory()
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # 网络信息
        network_io = psutil.net_io_counters()
        
        # 进程信息
        process = psutil.Process()
        process_memory = process.memory_info()
        
        metrics = {
            "cpu": {
                "percent": cpu_percent,
                "count": cpu_count,
                "load_avg": os.getloadavg() if hasattr(os, 'getloadavg') else None
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percentage": memory.percent,
                "process_rss": process_memory.rss,
                "process_vms": process_memory.vms
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percentage": (disk.used / disk.total) * 100,
                "read_bytes": disk_io.read_bytes if disk_io else 0,
                "write_bytes": disk_io.write_bytes if disk_io else 0
            },
            "network": {
                "bytes_sent": network_io.bytes_sent,
                "bytes_recv": network_io.bytes_recv,
                "packets_sent": network_io.packets_sent,
                "packets_recv": network_io.packets_recv
            }
        }
        
        return ApiResponse.success(
            data=metrics,
            message="获取系统指标成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.SYSTEM_ERROR,
            message=f"获取系统指标失败: {str(e)}"
        )


@router.post("/gc", response_model=ApiResponse[dict])
async def trigger_garbage_collection(
    settings = CurrentSettings
) -> ApiResponse[dict]:
    """触发垃圾回收
    
    手动触发Python垃圾回收。
    """
    try:
        import gc
        
        # 获取回收前的内存使用
        memory_before = psutil.virtual_memory().used
        
        # 执行垃圾回收
        collected = gc.collect()
        
        # 获取回收后的内存使用
        memory_after = psutil.virtual_memory().used
        memory_freed = memory_before - memory_after
        
        result = {
            "collected_objects": collected,
            "memory_before": memory_before,
            "memory_after": memory_after,
            "memory_freed": memory_freed
        }
        
        logger.info(f"垃圾回收完成: 回收对象{collected}个, 释放内存{memory_freed}字节")
        
        return ApiResponse.success(
            data=result,
            message="垃圾回收完成"
        )
        
    except Exception as e:
        logger.error(f"垃圾回收失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.SYSTEM_ERROR,
            message=f"垃圾回收失败: {str(e)}"
        )


async def get_system_statistics() -> Dict[str, Any]:
    """获取系统统计信息"""
    try:
        # TODO: 实现实际的统计查询
        from app.models.database import Images, Videos, Tasks
        
        # 模拟统计数据
        stats = {
            "total_images": 0,
            "total_videos": 0,
            "total_tasks": 0,
            "processed_images": 0,
            "processed_videos": 0,
            "failed_tasks": 0,
            "storage_used": 0
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return {}


@router.get("/logs", response_model=ApiResponse[list])
async def get_system_logs(
    level: str = "INFO",
    limit: int = 100,
    settings = CurrentSettings
) -> ApiResponse[list]:
    """获取系统日志
    
    返回最近的系统日志。
    """
    try:
        # TODO: 实现日志查询逻辑
        logger.info(f"获取系统日志: level={level}, limit={limit}")
        
        # 模拟日志数据
        logs = []
        
        return ApiResponse.success(
            data=logs,
            message="获取系统日志成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统日志失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.SYSTEM_ERROR,
            message=f"获取系统日志失败: {str(e)}"
        )
