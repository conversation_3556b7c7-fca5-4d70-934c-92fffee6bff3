"""
配置管理API路由

提供配置查询、更新、重载等功能的API端点。
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends

from app.models.schemas import (
    ConfigUpdateRequest,
    ConfigSectionUpdateRequest,
    ConfigReloadRequest,
    ConfigResetRequest
)
from app.utils.response import ApiResponse, ErrorCode
from app.core.dependencies import CurrentSettings, ConfigManager
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=ApiResponse[Dict[str, Any]])
async def get_all_config(
    section: Optional[str] = None,
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[Dict[str, Any]]:
    """获取所有配置或指定配置节"""
    try:
        if section:
            config_data = config_manager.get_section(section)
            if not config_data:
                return ApiResponse.error(
                    error_code=ErrorCode.RESOURCE_NOT_FOUND,
                    message=f"配置节 '{section}' 不存在"
                )
        else:
            config_data = config_manager.get_all_config()

        return ApiResponse.success(
            data=config_data,
            message="获取配置成功"
        )
        
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.CONFIG_LOAD_FAILED,
            message=f"获取配置失败: {str(e)}"
        )


@router.get("/{section}/{key}", response_model=ApiResponse[Any])
async def get_config_value(
    section: str,
    key: str,
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[Any]:
    """获取指定配置项的值"""
    try:
        value = config_manager.get_value(section, key)
        if value is None:
            return ApiResponse.error(
                error_code=ErrorCode.RESOURCE_NOT_FOUND,
                message=f"配置项 '{section}.{key}' 不存在"
            )

        return ApiResponse.success(
            data={
                "section": section,
                "key": key,
                "value": value,
                "is_dynamic": config_manager.is_dynamic_updatable(section, key)
            },
            message="获取配置项成功"
        )
        
    except Exception as e:
        logger.error(f"获取配置项失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.CONFIG_LOAD_FAILED,
            message=f"获取配置项失败: {str(e)}"
        )


@router.put("/{section}/{key}", response_model=ApiResponse[None])
async def update_config_value(
    section: str,
    key: str,
    request: ConfigUpdateRequest,
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[None]:
    """更新指定配置项的值"""
    try:
        # 验证配置项是否允许动态更新
        if not config_manager.is_dynamic_updatable(section, key):
            return ApiResponse.error(
                error_code=ErrorCode.OPERATION_FAILED,
                message=f"配置项 '{section}.{key}' 不支持动态更新"
            )

        # 验证配置值的有效性
        if not config_manager.validate_config_value(section, key, request.value):
            return ApiResponse.error(
                error_code=ErrorCode.CONFIG_VALIDATION_FAILED,
                message="配置值无效"
            )

        # 更新配置
        old_value = config_manager.get_value(section, key)
        config_manager.set_value(section, key, request.value)

        logger.info(f"配置已更新: {section}.{key} = {request.value}, 原因: {request.reason}")

        return ApiResponse.success(
            message="配置更新成功"
        )
        
    except Exception as e:
        logger.error(f"配置更新失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.CONFIG_UPDATE_FAILED,
            message=f"配置更新失败: {str(e)}"
        )


@router.put("/{section}", response_model=ApiResponse[None])
async def update_config_section(
    section: str,
    request: ConfigSectionUpdateRequest,
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[None]:
    """批量更新配置节"""
    try:
        # 验证配置节是否存在
        if not config_manager.has_section(section):
            return ApiResponse.error(
                error_code=ErrorCode.RESOURCE_NOT_FOUND,
                message=f"配置节 '{section}' 不存在"
            )

        # 批量验证和更新
        old_config = config_manager.get_section(section)
        
        for key, value in request.config.items():
            if not config_manager.is_dynamic_updatable(section, key):
                return ApiResponse.error(
                    error_code=ErrorCode.OPERATION_FAILED,
                    message=f"配置项 '{section}.{key}' 不支持动态更新"
                )

            if not config_manager.validate_config_value(section, key, value):
                return ApiResponse.error(
                    error_code=ErrorCode.CONFIG_VALIDATION_FAILED,
                    message=f"配置项 '{section}.{key}' 的值无效"
                )

        # 执行批量更新
        config_manager.update_section(section, request.config)

        logger.info(f"配置节已更新: {section}, 原因: {request.reason}")

        return ApiResponse.success(
            message="配置节更新成功"
        )
        
    except Exception as e:
        logger.error(f"配置节更新失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.CONFIG_UPDATE_FAILED,
            message=f"配置节更新失败: {str(e)}"
        )


@router.post("/reload", response_model=ApiResponse[None])
async def reload_config(
    request: ConfigReloadRequest,
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[None]:
    """重新加载配置文件"""
    try:
        if request.section:
            # 重载指定配置节
            config_manager.reload_section(request.section)
            message = f"配置节 '{request.section}' 重载成功"
        else:
            # 重载所有配置
            config_manager.reload_all()
            message = "所有配置重载成功"

        logger.info(f"配置重载: {request.section or '全部'}, 原因: {request.reason}")

        return ApiResponse.success(
            message=message
        )
        
    except Exception as e:
        logger.error(f"配置重载失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.CONFIG_LOAD_FAILED,
            message=f"配置重载失败: {str(e)}"
        )


@router.post("/reset", response_model=ApiResponse[None])
async def reset_config(
    request: ConfigResetRequest,
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[None]:
    """重置配置到默认值"""
    try:
        if request.section and request.key:
            # 重置指定配置项
            config_manager.reset_value(request.section, request.key)
            message = f"配置项 '{request.section}.{request.key}' 重置成功"
        elif request.section:
            # 重置指定配置节
            config_manager.reset_section(request.section)
            message = f"配置节 '{request.section}' 重置成功"
        else:
            # 重置所有动态配置
            config_manager.reset_all_dynamic()
            message = "所有动态配置重置成功"

        logger.info(f"配置重置: {request.section or '全部'}.{request.key or '全部'}, 原因: {request.reason}")

        return ApiResponse.success(
            message=message
        )
        
    except Exception as e:
        logger.error(f"配置重置失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.CONFIG_UPDATE_FAILED,
            message=f"配置重置失败: {str(e)}"
        )


@router.get("/schema", response_model=ApiResponse[Dict[str, Any]])
async def get_config_schema(
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[Dict[str, Any]]:
    """获取配置模式定义"""
    try:
        # TODO: 实现配置模式查询
        schema = {
            "sections": {
                "server": {
                    "description": "服务器配置",
                    "keys": {
                        "host": {"type": "str", "description": "服务器主机地址"},
                        "port": {"type": "int", "description": "服务器端口", "min": 1, "max": 65535},
                        "debug": {"type": "bool", "description": "调试模式"}
                    }
                },
                "database": {
                    "description": "数据库配置",
                    "keys": {
                        "host": {"type": "str", "description": "数据库主机"},
                        "port": {"type": "int", "description": "数据库端口"},
                        "name": {"type": "str", "description": "数据库名称"}
                    }
                }
            }
        }
        
        return ApiResponse.success(
            data=schema,
            message="获取配置模式成功"
        )
        
    except Exception as e:
        logger.error(f"获取配置模式失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.CONFIG_LOAD_FAILED,
            message=f"获取配置模式失败: {str(e)}"
        )


@router.get("/history", response_model=ApiResponse[list])
async def get_config_history(
    section: Optional[str] = None,
    key: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    config_manager = ConfigManager,
    settings = CurrentSettings
) -> ApiResponse[list]:
    """获取配置变更历史"""
    try:
        # TODO: 实现配置变更历史查询
        logger.info(f"获取配置变更历史: section={section}, key={key}")
        
        # 模拟历史记录
        history = []
        
        return ApiResponse.success(
            data=history,
            message="获取配置变更历史成功"
        )
        
    except Exception as e:
        logger.error(f"获取配置变更历史失败: {e}")
        return ApiResponse.error(
            error_code=ErrorCode.DATABASE_QUERY_FAILED,
            message=f"获取配置变更历史失败: {str(e)}"
        )
