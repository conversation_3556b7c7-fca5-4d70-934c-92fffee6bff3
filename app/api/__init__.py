"""
API路由模块

包含所有API端点的路由定义。
"""

from fastapi import APIRouter

from app.api import search, content, tasks, system, config

# 创建主路由器
api_router = APIRouter(prefix="/api")

# 注册子路由
api_router.include_router(search.router, prefix="/search", tags=["搜索"])
api_router.include_router(content.router, prefix="/content", tags=["内容管理"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(system.router, prefix="/system", tags=["系统管理"])
api_router.include_router(config.router, prefix="/config", tags=["配置管理"])

__all__ = ["api_router"]
