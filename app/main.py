"""
MaterialSearch 主应用

FastAPI应用的入口点，包含应用创建、中间件配置、路由注册等。
"""

import os
import sys
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.exception_handlers import http_exception_handler
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.config.settings import get_settings
from app.config.loader import ConfigLoader
from app.core.database import init_database, close_database
from app.api import api_router
from app.utils.logger import setup_logging, get_logger
from app.utils.response import ApiResponse, ErrorCode
from app.utils.exceptions import MaterialSearchException

# 获取配置
settings = get_settings()

# 设置日志
setup_logging(
    level=settings.logging.level,
    format_type=settings.logging.format,
    file_path=settings.logging.file_path if settings.logging.file_enabled else None,
    max_size=settings.logging.max_size,
    backup_count=settings.logging.backup_count,
    console_enabled=settings.logging.console_enabled,
    file_enabled=settings.logging.file_enabled
)

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("MaterialSearch 应用启动中...")
    
    try:
        # 加载配置
        config_loader = ConfigLoader()
        if not config_loader.validate_config(config_loader.load_config()):
            logger.error("配置验证失败")
            sys.exit(1)
        
        # 初始化数据库
        await init_database(settings)
        
        # TODO: 初始化模型管理器
        # TODO: 初始化搜索引擎
        # TODO: 启动后台任务
        
        logger.info("MaterialSearch 应用启动完成")
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        sys.exit(1)
    
    yield
    
    # 关闭时执行
    logger.info("MaterialSearch 应用关闭中...")
    
    try:
        # 关闭数据库连接
        await close_database()
        
        # TODO: 清理资源
        
        logger.info("MaterialSearch 应用关闭完成")
        
    except Exception as e:
        logger.error(f"应用关闭失败: {e}")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    # 创建应用实例
    app = FastAPI(
        title=settings.app.name,
        description=settings.app.description,
        version=settings.app.version,
        docs_url="/docs" if settings.server.debug else None,
        redoc_url="/redoc" if settings.server.debug else None,
        openapi_url="/openapi.json" if settings.server.debug else None,
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.server.debug else ["http://localhost:8000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 配置受信任主机
    if not settings.server.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", settings.server.host]
        )
    
    # 注册API路由
    app.include_router(api_router)
    
    # 静态文件服务
    static_dir = Path("static")
    if static_dir.exists():
        app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # 模板配置
    templates_dir = Path("templates")
    if templates_dir.exists():
        templates = Jinja2Templates(directory="templates")
        
        @app.get("/", response_class=HTMLResponse)
        async def index(request: Request):
            """主页"""
            return templates.TemplateResponse("index.html", {"request": request})
    
    # 全局异常处理
    @app.exception_handler(MaterialSearchException)
    async def material_search_exception_handler(request: Request, exc: MaterialSearchException):
        """处理自定义异常"""
        logger.error(f"业务异常: {exc}")
        return ApiResponse.error(
            error_code=exc.error_code,
            message=exc.message
        ).dict()
    
    @app.exception_handler(StarletteHTTPException)
    async def custom_http_exception_handler(request: Request, exc: StarletteHTTPException):
        """处理HTTP异常"""
        logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
        
        # 如果是API请求，返回JSON格式
        if request.url.path.startswith("/api/"):
            error_code = ErrorCode.UNKNOWN_ERROR
            if exc.status_code == 404:
                error_code = ErrorCode.RESOURCE_NOT_FOUND
            elif exc.status_code == 400:
                error_code = ErrorCode.INVALID_REQUEST
            elif exc.status_code == 401:
                error_code = ErrorCode.UNAUTHORIZED
            elif exc.status_code == 403:
                error_code = ErrorCode.PERMISSION_DENIED
            
            return ApiResponse.error(
                error_code=error_code,
                message=str(exc.detail)
            ).dict()
        
        # 其他请求使用默认处理
        return await http_exception_handler(request, exc)
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """全局异常处理"""
        logger.error(f"未处理异常: {exc}", exc_info=True)
        
        if request.url.path.startswith("/api/"):
            return ApiResponse.error(
                error_code=ErrorCode.SYSTEM_ERROR,
                message="系统内部错误"
            ).dict()
        
        raise HTTPException(status_code=500, detail="系统内部错误")
    
    # 请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """记录请求日志"""
        start_time = time.time()
        
        # 记录请求
        logger.info(f"请求开始: {request.method} {request.url}")
        
        try:
            response = await call_next(request)
            
            # 记录响应
            process_time = time.time() - start_time
            logger.info(f"请求完成: {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(f"请求失败: {request.method} {request.url} - {process_time:.3f}s - {e}")
            raise
    
    return app


# 创建应用实例
app = create_app()


def main():
    """主函数"""
    import uvicorn
    import time
    
    # 运行应用
    uvicorn.run(
        "app.main:app",
        host=settings.server.host,
        port=settings.server.port,
        reload=settings.server.reload and settings.server.debug,
        workers=1 if settings.server.debug else settings.server.workers,
        log_level=settings.logging.level.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()
