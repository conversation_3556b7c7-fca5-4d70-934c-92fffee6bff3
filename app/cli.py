"""
命令行工具

提供数据库初始化、扫描、配置管理等命令行功能。
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional

import click
from rich.console import Console
from rich.table import Table

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config.settings import get_settings
from app.core.database import init_database, check_database_connection
from app.utils.logger import setup_logging, get_logger

console = Console()


@click.group()
def cli():
    """MaterialSearch 命令行工具"""
    pass


@cli.command()
@click.option('--force', is_flag=True, help='强制重新初始化')
def init(force: bool):
    """初始化数据库"""
    async def _init():
        settings = get_settings()
        setup_logging(level="INFO", format_type="text", console_enabled=True, file_enabled=False)
        logger = get_logger(__name__)
        
        try:
            console.print("[bold blue]初始化数据库...[/bold blue]")
            
            # 检查数据库连接
            if not await check_database_connection(settings):
                console.print("[bold red]数据库连接失败[/bold red]")
                sys.exit(1)
            
            # 初始化数据库
            await init_database(settings)
            
            console.print("[bold green]数据库初始化完成！[/bold green]")
            
        except Exception as e:
            console.print(f"[bold red]初始化失败: {e}[/bold red]")
            sys.exit(1)
    
    asyncio.run(_init())


@cli.command()
@click.argument('paths', nargs=-1, required=True)
@click.option('--business-type', help='业务类型')
@click.option('--recursive', is_flag=True, help='递归扫描子目录')
def scan(paths: List[str], business_type: Optional[str], recursive: bool):
    """扫描指定路径的文件"""
    console.print(f"[bold blue]扫描路径: {', '.join(paths)}[/bold blue]")
    
    if business_type:
        console.print(f"业务类型: {business_type}")
    
    if recursive:
        console.print("递归扫描: 是")
    
    # TODO: 实现扫描逻辑
    console.print("[bold yellow]扫描功能暂未实现[/bold yellow]")


@cli.command()
@click.option('--section', help='配置节名称')
@click.option('--format', 'output_format', default='table', type=click.Choice(['table', 'json', 'yaml']), help='输出格式')
def config(section: Optional[str], output_format: str):
    """查看配置信息"""
    async def _config():
        from app.config.loader import ConfigLoader
        
        try:
            loader = ConfigLoader()
            config_data = loader.load_config()
            
            if section:
                if section in config_data:
                    data = {section: config_data[section]}
                else:
                    console.print(f"[bold red]配置节 '{section}' 不存在[/bold red]")
                    return
            else:
                data = config_data
            
            if output_format == 'table':
                _display_config_table(data)
            elif output_format == 'json':
                import json
                console.print(json.dumps(data, indent=2, ensure_ascii=False))
            elif output_format == 'yaml':
                import yaml
                console.print(yaml.dump(data, default_flow_style=False, allow_unicode=True))
                
        except Exception as e:
            console.print(f"[bold red]获取配置失败: {e}[/bold red]")
    
    asyncio.run(_config())


def _display_config_table(config_data: dict):
    """以表格形式显示配置"""
    table = Table(title="配置信息")
    table.add_column("配置节", style="cyan")
    table.add_column("配置项", style="magenta")
    table.add_column("值", style="green")
    
    for section_name, section_data in config_data.items():
        if isinstance(section_data, dict):
            for key, value in section_data.items():
                table.add_row(section_name, key, str(value))
        else:
            table.add_row(section_name, "", str(section_data))
    
    console.print(table)


@cli.command()
def status():
    """查看系统状态"""
    async def _status():
        try:
            settings = get_settings()
            
            # 创建状态表格
            table = Table(title="系统状态")
            table.add_column("项目", style="cyan")
            table.add_column("状态", style="green")
            
            # 应用信息
            table.add_row("应用名称", settings.app.name)
            table.add_row("应用版本", settings.app.version)
            table.add_row("运行环境", settings.environment)
            
            # 数据库状态
            db_connected = await check_database_connection(settings)
            table.add_row("数据库连接", "✓ 正常" if db_connected else "✗ 失败")
            
            # 配置信息
            table.add_row("服务器地址", f"{settings.server.host}:{settings.server.port}")
            table.add_row("调试模式", "启用" if settings.server.debug else "禁用")
            table.add_row("日志级别", settings.logging.level)
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[bold red]获取状态失败: {e}[/bold red]")
    
    asyncio.run(_status())


@cli.command()
@click.option('--host', default=None, help='服务器主机地址')
@click.option('--port', default=None, type=int, help='服务器端口')
@click.option('--reload', is_flag=True, help='启用自动重载')
@click.option('--workers', default=None, type=int, help='工作进程数')
def serve(host: Optional[str], port: Optional[int], reload: bool, workers: Optional[int]):
    """启动服务器"""
    import uvicorn
    from app.main import app
    
    settings = get_settings()
    
    # 使用命令行参数覆盖配置
    server_host = host or settings.server.host
    server_port = port or settings.server.port
    server_reload = reload or (settings.server.reload and settings.server.debug)
    server_workers = workers or (1 if settings.server.debug else settings.server.workers)
    
    console.print(f"[bold blue]启动服务器: http://{server_host}:{server_port}[/bold blue]")
    
    uvicorn.run(
        "app.main:app",
        host=server_host,
        port=server_port,
        reload=server_reload,
        workers=1 if server_reload else server_workers,
        log_level=settings.logging.level.lower()
    )


@cli.command()
def version():
    """显示版本信息"""
    settings = get_settings()
    
    table = Table(title="版本信息")
    table.add_column("组件", style="cyan")
    table.add_column("版本", style="green")
    
    table.add_row("MaterialSearch", settings.app.version)
    
    try:
        import fastapi
        table.add_row("FastAPI", fastapi.__version__)
    except ImportError:
        pass
    
    try:
        import tortoise
        table.add_row("Tortoise ORM", tortoise.__version__)
    except ImportError:
        pass
    
    try:
        import torch
        table.add_row("PyTorch", torch.__version__)
    except ImportError:
        pass
    
    console.print(table)


# 命令别名
init_command = init
scan_command = scan
config_command = config


if __name__ == '__main__':
    cli()
