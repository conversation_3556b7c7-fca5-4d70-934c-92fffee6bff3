# MaterialSearch Docker Compose 配置
version: '3.8'

services:
  # MaterialSearch 主应用
  materialsearch:
    build: .
    container_name: materialsearch-app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=materialsearch
      - DB_USER=materialsearch
      - DB_PASSWORD=materialsearch_password
    volumes:
      - ./config:/app/config:ro
      - ./data:/data:ro
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./models:/app/models
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - materialsearch-network

  # PostgreSQL 数据库
  postgres:
    image: pgvector/pgvector:pg14
    container_name: materialsearch-db
    environment:
      - POSTGRES_DB=materialsearch
      - POSTGRES_USER=materialsearch
      - POSTGRES_PASSWORD=materialsearch_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U materialsearch -d materialsearch"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - materialsearch-network

  # Redis (可选，用于缓存)
  redis:
    image: redis:7-alpine
    container_name: materialsearch-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - materialsearch-network

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: materialsearch-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - materialsearch
    restart: unless-stopped
    networks:
      - materialsearch-network

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# 网络
networks:
  materialsearch-network:
    driver: bridge
